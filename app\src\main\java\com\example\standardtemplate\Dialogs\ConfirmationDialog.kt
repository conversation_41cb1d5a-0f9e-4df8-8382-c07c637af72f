package com.example.standardtemplate.Dialogs

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.Window
import android.widget.Button
import android.widget.TextView
import com.example.standardtemplate.R

object ConfirmationDialog {
    /**
     * showConfirmDialog function --> show a confirmation dialog with a title and a message
     * @param context the context of the activity or fragment
     * @param title the title of the dialog
     * @param message the message to be displayed in the dialog
     * @param onConfirm the callback function to be executed when the "Confirm" button is clicked
     */
    fun showConfirmDialog(context: Context, title: String, message: String, onConfirm: () -> Unit) {
        val dialog = Dialog(context)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_confirmation, null)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(view)

        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvMessage = view.findViewById<TextView>(R.id.tvMessage)
        val btnCancel = view.findViewById<Button>(R.id.btnCancel)
        val btnConfirm = view.findViewById<Button>(R.id.btnConfirm)

        tvTitle.text = title
        tvMessage.text = message

        btnConfirm.setOnClickListener {
            onConfirm()
            dialog.dismiss()
        }

        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

}
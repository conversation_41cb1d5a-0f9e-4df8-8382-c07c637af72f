<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Camera Applet Demo Layout -->
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true"
    tools:context=".Activities.RealWear.CameraAppletActivity">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_camera_applet_title"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="4dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_camera_applet_description"
        android:textSize="12sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp"
        android:lineSpacingMultiplier="1.1" />

    <!-- Image Preview Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="12dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_preview_label"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="6dp" />

        <!-- Image View for captured photos -->
        <ImageView
            android:id="@+id/camera_image_view"
            android:layout_width="280dp"
            android:layout_height="180dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/rounded_background_light"
            android:scaleType="centerCrop"
            android:contentDescription="@string/realwear_camera_preview_description"
            android:src="@drawable/ic_close" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_preview_instruction"
            android:textSize="10sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center" />

    </LinearLayout>

    <!-- Camera Action Buttons Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Photo Capture Buttons -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_photo_section"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="6dp" />

        <Button
            android:id="@+id/btnBitmapPhoto"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:text="@string/realwear_camera_bitmap_photo"
            android:textSize="13sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="6dp"
            android:gravity="center" />

        <Button
            android:id="@+id/btnFileProviderPhoto"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:text="@string/realwear_camera_file_provider_photo"
            android:textSize="13sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="10dp"
            android:gravity="center" />

        <!-- Video Recording Buttons -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_video_section"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="6dp" />

        <Button
            android:id="@+id/btnBasicVideo"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:text="@string/realwear_camera_basic_video"
            android:textSize="13sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="6dp"
            android:gravity="center" />

        <Button
            android:id="@+id/btnFileProviderVideo"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:text="@string/realwear_camera_file_provider_video"
            android:textSize="13sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp"
            android:gravity="center" />

        <!-- Back Button -->
        <Button
            android:id="@+id/btnBack"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:text="@string/btnBack"
            android:textSize="14sp"
            android:background="@drawable/dialog_background"
            android:textColor="@color/black"
            android:drawableStart="@drawable/ic_back"
            android:drawablePadding="8dp"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>

</ScrollView>

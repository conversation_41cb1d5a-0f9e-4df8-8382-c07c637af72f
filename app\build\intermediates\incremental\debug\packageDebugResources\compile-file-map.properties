#Tue Aug 19 15:41:33 MYT 2025
com.example.standardtemplate.app-main-6\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\application_icon.png
com.example.standardtemplate.app-main-6\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_back.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_noti_background.xml
com.example.standardtemplate.app-main-6\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\input_field_background.xml
com.example.standardtemplate.app-main-6\:/drawable/radio_button_red.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\radio_button_red.xml
com.example.standardtemplate.app-main-6\:/drawable/radio_button_unchecked.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\radio_button_unchecked.xml
com.example.standardtemplate.app-main-6\:/drawable/rounded_background_light.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_background_light.xml
com.example.standardtemplate.app-main-6\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_accepted_ticket_details.xml
com.example.standardtemplate.app-main-6\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_accepted_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_login.xml
com.example.standardtemplate.app-main-6\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_new_ticket_details.xml
com.example.standardtemplate.app-main-6\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_new_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_action_button.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_action_button.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_audio_capture.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_audio_capture.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_barcode_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_barcode_applet.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_bnf_grammar.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_bnf_grammar.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_camera_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_camera_applet.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_document_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_document_applet.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_help_menu.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_help_menu.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_keyboard_dictation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_keyboard_dictation.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_main.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_main.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_microphone_release.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_microphone_release.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_movie_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_movie_applet.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_speech_recognizer.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_speech_recognizer.xml
com.example.standardtemplate.app-main-6\:/layout/activity_realwear_text_to_speech.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_realwear_text_to_speech.xml
com.example.standardtemplate.app-main-6\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_register.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_confirmation.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_message.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_setting.xml
com.example.standardtemplate.app-main-6\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_accepted_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\recycle_view_new_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_item.xml
com.example.standardtemplate.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.standardtemplate.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.standardtemplate.app-main-6\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.standardtemplate.app-main-6\:/xml/file_provider_paths.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_provider_paths.xml
com.example.standardtemplate.app-main-6\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\locales_config.xml

com.example.standardtemplate.app-emoji2-1.3.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\res
com.example.standardtemplate.app-emoji2-views-helper-1.3.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0907741920ba27261b68d6942f7607bc\transformed\emoji2-views-helper-1.3.0\res
com.example.standardtemplate.app-room-runtime-2.5.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\res
com.example.standardtemplate.app-lifecycle-livedata-core-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\10f11d1ba46eb5863202a4dba7257a70\transformed\lifecycle-livedata-core-2.6.2\res
com.example.standardtemplate.app-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\16ae8f7c219696ed3a6efe1ce9c31690\transformed\startup-runtime-1.1.1\res
com.example.standardtemplate.app-lifecycle-livedata-2.6.2-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1a2214d123087fdd9a9ec68b3df82047\transformed\lifecycle-livedata-2.6.2\res
com.example.standardtemplate.app-constraintlayout-2.2.1-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ba89cba7417c6725234382ffa717299\transformed\constraintlayout-2.2.1\res
com.example.standardtemplate.app-cardview-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\22e22a672941fce55110ffe0b8066895\transformed\cardview-1.0.0\res
com.example.standardtemplate.app-core-viewtree-1.0.0-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\31828beeb6b7b51a80534e87d678d0d6\transformed\core-viewtree-1.0.0\res
com.example.standardtemplate.app-lifecycle-service-2.6.2-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3299f5536dbf487f656c953a266811c8\transformed\lifecycle-service-2.6.2\res
com.example.standardtemplate.app-work-runtime-2.9.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\res
com.example.standardtemplate.app-sqlite-2.3.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\461f677bb86156d6a2dc1a538b88e24d\transformed\sqlite-2.3.0\res
com.example.standardtemplate.app-play-services-basement-18.1.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\res
com.example.standardtemplate.app-lifecycle-viewmodel-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\4dd143acdeca3562f716e92451c15e0e\transformed\lifecycle-viewmodel-2.6.2\res
com.example.standardtemplate.app-fragment-1.5.4-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ff01f55946e98ba5590edbee9d55252\transformed\fragment-1.5.4\res
com.example.standardtemplate.app-appcompat-1.7.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ff4ec66daf8ab6ce732fa9c600777\transformed\appcompat-1.7.0\res
com.example.standardtemplate.app-core-1.15.0-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\res
com.example.standardtemplate.app-recyclerview-1.1.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\6e3d759341d5d145ead45457f9df9377\transformed\recyclerview-1.1.0\res
com.example.standardtemplate.app-coordinatorlayout-1.1.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb7ed0ae63d3c49fffa8ba99a43763f\transformed\coordinatorlayout-1.1.0\res
com.example.standardtemplate.app-room-ktx-2.5.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\77803addd44dc3665a5b9680dd40c047\transformed\room-ktx-2.5.0\res
com.example.standardtemplate.app-material-1.12.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\838052c77a74c243fbc236b2a83bb29c\transformed\material-1.12.0\res
com.example.standardtemplate.app-play-services-base-18.0.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\res
com.example.standardtemplate.app-profileinstaller-1.4.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\res
com.example.standardtemplate.app-appcompat-resources-1.7.0-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\88fe8a244b19b211d69544debf26b711\transformed\appcompat-resources-1.7.0\res
com.example.standardtemplate.app-savedstate-1.2.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\93e4689b40fa05b9ac900f83fcd43ca1\transformed\savedstate-1.2.1\res
com.example.standardtemplate.app-lifecycle-viewmodel-savedstate-2.6.2-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab93327de0b24d77747d081dda63b8d\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.standardtemplate.app-activity-1.10.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\a33f5a5b546bf04a02987ba6cda949f5\transformed\activity-1.10.1\res
com.example.standardtemplate.app-firebase-messaging-23.1.2-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\res
com.example.standardtemplate.app-drawerlayout-1.1.1-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\adaf6fa70fd7f06c6531b10716aa2088\transformed\drawerlayout-1.1.1\res
com.example.standardtemplate.app-sqlite-framework-2.3.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\b1c4cf7b65312869f3edf087392f4cdf\transformed\sqlite-framework-2.3.0\res
com.example.standardtemplate.app-transition-1.5.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\b7d6b3a42b5537fcce4e028110689293\transformed\transition-1.5.0\res
com.example.standardtemplate.app-core-ktx-1.15.0-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\c8dd94f69513de21c20b06bf0e3430c7\transformed\core-ktx-1.15.0\res
com.example.standardtemplate.app-lifecycle-process-2.6.2-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\res
com.example.standardtemplate.app-core-runtime-2.2.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\d228771ea6241ed59723249bfec0d9be\transformed\core-runtime-2.2.0\res
com.example.standardtemplate.app-tracing-1.2.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\d40dc1faef1ddf4a72793256276a703d\transformed\tracing-1.2.0\res
com.example.standardtemplate.app-work-runtime-ktx-2.9.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\d79c492308a15d402cc6e0df535f03ea\transformed\work-runtime-ktx-2.9.0\res
com.example.standardtemplate.app-lifecycle-runtime-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\dedefce7f97ca08935579a0d0ad5bb96\transformed\lifecycle-runtime-2.6.2\res
com.example.standardtemplate.app-firebase-common-20.3.2-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\res
com.example.standardtemplate.app-annotation-experimental-1.4.1-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\eb83a6a2d7e7002fe90d8f79e2328660\transformed\annotation-experimental-1.4.1\res
com.example.standardtemplate.app-glide-4.16.0-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0ccbe3f726404611b02120431db826\transformed\glide-4.16.0\res
com.example.standardtemplate.app-viewpager2-1.0.0-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb34b569ef3258e736253342526ac408\transformed\viewpager2-1.0.0\res
com.example.standardtemplate.app-pngs-41 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\pngs\debug
com.example.standardtemplate.app-res-42 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\processDebugGoogleServices
com.example.standardtemplate.app-resValues-43 D:\MobileProject\standardtemplate-kotlin\app\build\generated\res\resValues\debug
com.example.standardtemplate.app-packageDebugResources-44 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.standardtemplate.app-packageDebugResources-45 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.standardtemplate.app-debug-46 D:\MobileProject\standardtemplate-kotlin\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.standardtemplate.app-debug-47 D:\MobileProject\standardtemplate-kotlin\app\src\debug\res
com.example.standardtemplate.app-main-48 D:\MobileProject\standardtemplate-kotlin\app\src\main\res

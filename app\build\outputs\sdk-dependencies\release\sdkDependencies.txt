# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.23"
  }
  digests {
    sha256: "\211\020\314#\210\a\330n\365P\313\037\v\020\335^\324\v5\244\354\032RR_v\n\355\350N\2557"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.23"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.0"
  }
  digests {
    sha256: "\245\237\242O\337\037\373YK\256\315\277\017\321\000\020\371w\316\241\0026\324\207\3764d\227zsw\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.0"
  }
  digests {
    sha256: "\267\227\232z\254\224\005_\r\237\037\323\264|\345\377\341\313`2\250B\272\237\276q\206\360\205(\221x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "5.0.0-alpha.14"
  }
  digests {
    sha256: "\360\343\377\313\272gD\253I\030\245Z\243/\364\364\b\374\322\036E\253\213\325\367\210:\027\223\262\242S"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.9.0"
  }
  digests {
    sha256: "\335\303\206\377\024\275%\325\3114\026q\226\352\364[\030\336O(\341\305ZM\263z\345\224\313\3757\344"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.11.0"
  }
  digests {
    sha256: "=+Kf\211\tF\204e\305wd0\265r2\']\001eQ? \374\226\260\317\344\252S\023&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "5.0.0-alpha.14"
  }
  digests {
    sha256: "\277\362c\321P,\b2?9\272\244\357&\240\345d\340\367\372g+\363\370_\233\333\244\322\235\357\311"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.5.0"
  }
  digests {
    sha256: "\020\367m\365b\"SL{\370|\230d=j\017\333~\2354#\226\246y\303\025\341\0206\260E\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.0"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\230\202\204\030w\244C\177 \002\355\2355?uzTR\341;J\260\353\361\225\306<\317\016.\bG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.0"
  }
  digests {
    sha256: "\323\323~$\003\305#\245\316\341\230;\'\246\336z\330\334\273P/Dl?v\374\245\016\327<\345b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.0"
  }
  digests {
    sha256: "\213[\323\254\357\001\352x\032\205E\275\367\020\261\300a\202Avi\241\246\214\320\256JSl\332\350\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "23.1.2"
  }
  digests {
    sha256: "\314\333\3042\a)_y_\332\267X>\276\227\266 \016\274\211\276\201\364hi\0324\2463\267\220D"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "20.3.2"
  }
  digests {
    sha256: "\253\303O\2562PG=\246\354=\262\336\234\037\313$\375\224\330\231\341\377\216\006\217\243\234k\246\361\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "17.1.0"
  }
  digests {
    sha256: "5,q\370\231\224\313D\201\332\353E\35560\273\361x\233\337\033\344V9\210\342\301\206\030\257O`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.1.0"
  }
  digests {
    sha256: "\003\272\025\363\206\317&\v\336r\374@\020\373\211\250 \304}Q\316c\274\t)~\253\346\314\340\233\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.2"
  }
  digests {
    sha256: "\003F\\\3656\200\332#\323\324\243\231\\\003)\267\367;!\0358y\036\3136\345\221\376\273\211fd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\211\037>\247\231\273?9\262K\371K\266\331QBM\336xK\\\233Z\361\336\364\272\267\230p]\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.1.7"
  }
  digests {
    sha256: "_#\325u\n\342H\334\2421,Yc\252\211\370Q\2766\023/\270\237\025\021L5\205\346\005\377\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.0.0"
  }
  digests {
    sha256: "Ni\203\300p;5}\366\361\306\316\254\261\265\337\302\305\000jx\234y\237\354\"\230\262\2653tf"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.8"
  }
  digests {
    sha256: "\341~\335\036\367\375G\\\220\272\244\343\224\"3/\'\b}4\274\264l\264\214\350j\371\245Ja."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.8"
  }
  digests {
    sha256: "\313\223S\357\027\221\256\027\t}\207\214\247\021\342Z\2342\316\311\004*\334I\260\f\255\376\341\247)\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.1.3"
  }
  digests {
    sha256: "nQw\375\220Mm\300\265H\231C\347_Q/,\300\327JM\300J\002P\005\237\232\\\206B6"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.0.1"
  }
  digests {
    sha256: "\036u\232\334\3605\a1\316M\307;\003W\005\344\307\320\213\337}\260i\314\004h\354\243\347\273\235\302"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "32.1.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "21.3.0"
  }
  digests {
    sha256: "f(?\375Ut\021\267\033\274[m:\347\315\215\342\3443\316\241\226\334I\314\032\230\311\371\274\303f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "21.3.0"
  }
  digests {
    sha256: "\364\343\311\271\314xW\202\323\245\030\202Qc\243\340\327!\237\200\266Xw\324\375\376v\027z\256\215\246"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "21.3.0"
  }
  digests {
    sha256: "\312V>R\362\324\314\003c`\305\213\276_E\033[(W2O\314jD~t\354\352\r\241\356\367"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "21.3.0"
  }
  digests {
    sha256: "\023m~\027\324\212U\000\017\006\247r]\262\227\264\355\237F\230g1\324\204{\224\223\323E>\200\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "21.3.0"
  }
  digests {
    sha256: "\221\215\217\215V*J]\264\233\310\336\317af\001\214\333mB\037\021\372A1\366\000\034\b\260\321|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "21.3.0"
  }
  digests {
    sha256: "\213c\314\315\326(E\270\253\206,\264\273\261z;X\264\004\004L\"\264\354s\257I\354\266\2271\265"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "21.3.0"
  }
  digests {
    sha256: ">\374B\205\355:\374\273~\025\3731\226\277\271\324\353]Mq\337W}o\356\356\r\225%x\336n"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 27
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 3
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 13
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 16
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 4
  library_dep_index: 22
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 22
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 20
}
library_dependencies {
  library_index: 23
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 24
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
}
library_dependencies {
  library_index: 28
  library_dep_index: 15
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 15
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 3
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 26
  library_dep_index: 13
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 37
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 48
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 37
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 18
  library_dep_index: 15
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 27
  library_dep_index: 3
  library_dep_index: 20
  library_dep_index: 3
}
library_dependencies {
  library_index: 36
  library_dep_index: 3
  library_dep_index: 3
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 34
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 39
  library_dep_index: 38
  library_dep_index: 14
  library_dep_index: 10
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 44
}
library_dependencies {
  library_index: 44
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 43
  library_dep_index: 43
}
library_dependencies {
  library_index: 45
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
  library_dep_index: 31
  library_dep_index: 47
  library_dep_index: 3
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 23
  library_dep_index: 29
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 42
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 35
  library_dep_index: 1
  library_dep_index: 34
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 56
  library_dep_index: 9
  library_dep_index: 45
  library_dep_index: 15
  library_dep_index: 61
  library_dep_index: 48
  library_dep_index: 62
  library_dep_index: 38
  library_dep_index: 63
}
library_dependencies {
  library_index: 50
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 42
  library_dep_index: 10
}
library_dependencies {
  library_index: 54
  library_dep_index: 34
  library_dep_index: 55
  library_dep_index: 8
  library_dep_index: 32
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
}
library_dependencies {
  library_index: 56
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 46
  library_dep_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 1
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
}
library_dependencies {
  library_index: 60
  library_dep_index: 1
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 42
  library_dep_index: 10
}
library_dependencies {
  library_index: 62
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 56
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
  library_dep_index: 45
  library_dep_index: 61
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
  library_dep_index: 3
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 3
}
library_dependencies {
  library_index: 68
  library_dep_index: 64
  library_dep_index: 69
}
library_dependencies {
  library_index: 70
  library_dep_index: 65
  library_dep_index: 3
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 45
  library_dep_index: 39
  library_dep_index: 75
  library_dep_index: 27
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
}
library_dependencies {
  library_index: 75
  library_dep_index: 1
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 9
  library_dep_index: 8
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 26
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 76
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 3
  library_dep_index: 19
}
library_dependencies {
  library_index: 79
  library_dep_index: 1
  library_dep_index: 6
}
library_dependencies {
  library_index: 80
  library_dep_index: 9
  library_dep_index: 17
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 81
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
  library_dep_index: 81
  library_dep_index: 3
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 87
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 94
  library_dep_index: 93
  library_dep_index: 95
  library_dep_index: 97
  library_dep_index: 96
  library_dep_index: 98
  library_dep_index: 1
  library_dep_index: 89
  library_dep_index: 88
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 51
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 87
  library_dep_index: 12
  library_dep_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 51
  library_dep_index: 85
}
library_dependencies {
  library_index: 88
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 45
}
library_dependencies {
  library_index: 89
  library_dep_index: 88
}
library_dependencies {
  library_index: 90
  library_dep_index: 89
  library_dep_index: 85
}
library_dependencies {
  library_index: 91
  library_dep_index: 1
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 84
  library_dep_index: 87
}
library_dependencies {
  library_index: 92
  library_dep_index: 1
}
library_dependencies {
  library_index: 93
  library_dep_index: 1
  library_dep_index: 92
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 97
}
library_dependencies {
  library_index: 94
  library_dep_index: 1
  library_dep_index: 92
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 86
}
library_dependencies {
  library_index: 95
  library_dep_index: 1
}
library_dependencies {
  library_index: 96
  library_dep_index: 1
  library_dep_index: 95
}
library_dependencies {
  library_index: 97
  library_dep_index: 1
  library_dep_index: 95
}
library_dependencies {
  library_index: 98
  library_dep_index: 84
  library_dep_index: 90
  library_dep_index: 87
  library_dep_index: 85
  library_dep_index: 89
}
library_dependencies {
  library_index: 99
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 100
  library_dep_index: 57
  library_dep_index: 88
}
library_dependencies {
  library_index: 101
  library_dep_index: 88
  library_dep_index: 85
}
library_dependencies {
  library_index: 102
  library_dep_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 103
  library_dep_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 83
  library_dep_index: 84
  library_dep_index: 95
  library_dep_index: 98
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
  library_dep_index: 110
  library_dep_index: 112
}
library_dependencies {
  library_index: 106
  library_dep_index: 10
  library_dep_index: 57
  library_dep_index: 107
  library_dep_index: 88
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 100
}
library_dependencies {
  library_index: 107
  library_dep_index: 88
}
library_dependencies {
  library_index: 108
  library_dep_index: 88
}
library_dependencies {
  library_index: 109
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 107
  library_dep_index: 88
  library_dep_index: 108
  library_dep_index: 100
}
library_dependencies {
  library_index: 110
  library_dep_index: 107
  library_dep_index: 88
  library_dep_index: 108
  library_dep_index: 111
  library_dep_index: 89
  library_dep_index: 84
  library_dep_index: 87
  library_dep_index: 98
  library_dep_index: 90
  library_dep_index: 101
}
library_dependencies {
  library_index: 111
  library_dep_index: 88
  library_dep_index: 108
}
library_dependencies {
  library_index: 112
  library_dep_index: 10
  library_dep_index: 88
  library_dep_index: 108
  library_dep_index: 109
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 34
  dependency_index: 49
  dependency_index: 35
  dependency_index: 54
  dependency_index: 3
  dependency_index: 8
  dependency_index: 64
  dependency_index: 68
  dependency_index: 65
  dependency_index: 70
  dependency_index: 71
  dependency_index: 69
  dependency_index: 76
  dependency_index: 83
  dependency_index: 104
  dependency_index: 105
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}

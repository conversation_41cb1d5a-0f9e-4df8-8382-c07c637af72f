<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/main"
    android:layout_width="350dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@drawable/dialog_background"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="50dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text="@string/reminder"
        android:textColor="@color/primaryColor"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvMessage"
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/reminder"
        android:textColor="@android:color/darker_gray"
        android:textSize="14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnOk"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:backgroundTint="@color/primaryDarkColor"
            android:text="@string/confirm"
            android:textColor="@android:color/white"
            />

    </LinearLayout>
</LinearLayout>

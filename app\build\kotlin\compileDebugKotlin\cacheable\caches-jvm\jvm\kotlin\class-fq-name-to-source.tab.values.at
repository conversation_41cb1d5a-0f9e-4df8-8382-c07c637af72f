/ Header Record For PersistentHashMapValueStoragej iapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketDetailsActivity.ktg fapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketListActivity.ktj iapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketAdapter.ktj iapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketAdapter.ktf eapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketDto.ktf eapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketDto.kt` _app/src/main/java/com/example/standardtemplate/Activities/Login_Setting/Dialog/SettingDialog.ktY Xapp/src/main/java/com/example/standardtemplate/Activities/Login_Setting/LoginActivity.kta `app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Adapter/NewTicketAdapter.kta `app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Adapter/NewTicketAdapter.kt\ [app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Dialog/CreateDialog.ktg fapp/src/main/java/com/example/standardtemplate/Activities/NewTickets/Interface/CreateDialogListener.kta `app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketDetailsActivity.kt^ ]app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketListActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/ActionButtonActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/ActionButtonActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/AudioCaptureActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/AudioCaptureActivity.ktY Xapp/src/main/java/com/example/standardtemplate/Activities/RealWear/BNFGrammarActivity.ktY Xapp/src/main/java/com/example/standardtemplate/Activities/RealWear/BNFGrammarActivity.kt\ [app/src/main/java/com/example/standardtemplate/Activities/RealWear/BarcodeAppletActivity.kt\ [app/src/main/java/com/example/standardtemplate/Activities/RealWear/BarcodeAppletActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/CameraAppletActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/CameraAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.ktW Vapp/src/main/java/com/example/standardtemplate/Activities/RealWear/HelpMenuActivity.ktW Vapp/src/main/java/com/example/standardtemplate/Activities/RealWear/HelpMenuActivity.kt` _app/src/main/java/com/example/standardtemplate/Activities/RealWear/KeyboardDictationActivity.kt` _app/src/main/java/com/example/standardtemplate/Activities/RealWear/KeyboardDictationActivity.kt` _app/src/main/java/com/example/standardtemplate/Activities/RealWear/MicrophoneReleaseActivity.kt` _app/src/main/java/com/example/standardtemplate/Activities/RealWear/MicrophoneReleaseActivity.ktZ Yapp/src/main/java/com/example/standardtemplate/Activities/RealWear/MovieAppletActivity.ktZ Yapp/src/main/java/com/example/standardtemplate/Activities/RealWear/MovieAppletActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/RealWearMainActivity.kt_ ^app/src/main/java/com/example/standardtemplate/Activities/RealWear/SpeechRecognizerActivity.kt_ ^app/src/main/java/com/example/standardtemplate/Activities/RealWear/SpeechRecognizerActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/TextToSpeechActivity.kt[ Zapp/src/main/java/com/example/standardtemplate/Activities/RealWear/TextToSpeechActivity.ktW Vapp/src/main/java/com/example/standardtemplate/Activities/Register/RegisterActivity.ktM Lapp/src/main/java/com/example/standardtemplate/Dialogs/ConfirmationDialog.ktH Gapp/src/main/java/com/example/standardtemplate/Dialogs/MessageDialog.ktF Eapp/src/main/java/com/example/standardtemplate/Libraries/ApiClient.ktI Happ/src/main/java/com/example/standardtemplate/Libraries/ApiInterface.ktU Tapp/src/main/java/com/example/standardtemplate/Libraries/FirebaseMessagingService.ktY Xapp/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/BaseActivity.ktY Xapp/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/BaseActivity.kt] \app/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/StandardTemplate.ktU Tapp/src/main/java/com/example/standardtemplate/Libraries/Managers/UserInfoManager.ktO Napp/src/main/java/com/example/standardtemplate/Libraries/NotificationHelper.ktP Oapp/src/main/java/com/example/standardtemplate/Libraries/Notificationservice.ktS Rapp/src/main/java/com/example/standardtemplate/Libraries/RealWear/DocumentUtils.ktX Wapp/src/main/java/com/example/standardtemplate/Libraries/StandardFunction/SharedPref.ktM Lapp/src/main/java/com/example/standardtemplate/Libraries/TicketMonitoring.ktM Lapp/src/main/java/com/example/standardtemplate/Libraries/TicketMonitoring.ktK Japp/src/main/java/com/example/standardtemplate/Models/AcceptedTicketDto.ktK Japp/src/main/java/com/example/standardtemplate/Models/AcceptedTicketDto.ktI Happ/src/main/java/com/example/standardtemplate/Models/FcmTokenRequest.ktC Bapp/src/main/java/com/example/standardtemplate/Models/LoginInfo.ktG Fapp/src/main/java/com/example/standardtemplate/Models/LoginResponse.ktG Fapp/src/main/java/com/example/standardtemplate/Models/NewTicketInfo.ktO Napp/src/main/java/com/example/standardtemplate/Models/NewTicketListResponse.ktF Eapp/src/main/java/com/example/standardtemplate/Models/RegisterInfo.ktJ Iapp/src/main/java/com/example/standardtemplate/Models/RegisterResponse.ktD Capp/src/main/java/com/example/standardtemplate/Models/TicketDone.ktE Dapp/src/main/java/com/example/standardtemplate/Models/UserDetails.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.ktS Rapp/src/main/java/com/example/standardtemplate/Libraries/RealWear/DocumentUtils.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.kt] \app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.ktS Rapp/src/main/java/com/example/standardtemplate/Libraries/RealWear/DocumentUtils.kt
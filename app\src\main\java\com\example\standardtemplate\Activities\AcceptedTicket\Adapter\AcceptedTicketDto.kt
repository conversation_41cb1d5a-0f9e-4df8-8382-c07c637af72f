package com.example.standardtemplate.Activities.AcceptedTicket.Adapter

import com.google.gson.annotations.SerializedName

data class AcceptedTicketDto(
    @SerializedName("Id") val id: Int,
    @SerializedName("ErrorMsg") val errorMsg: String?,
    @SerializedName("MachineId") val machineId: String?,
    @SerializedName("MachineStatus") val machineStatus: String?,
    @SerializedName("CreatedAt") val createdAt: String?,
    @SerializedName("CreatedBy") val createdBy: Int?,
    @SerializedName("Remedy") val remedy: String?,
    @SerializedName("AckAt") val ackAt: String?,
    @SerializedName("CompleteAt") val completeAt: String?,
    @SerializedName("AttendBy") val attendBy: Int?,
    @SerializedName("Status") val status: String?,
    @SerializedName("DowntimeTime") val machineDowntime: String,
    @SerializedName("DowntimeStr") val machineDowntimeStr: String,
    @SerializedName("AttendByNavigation") val attendByNavigation: UserNavigation?,
    @SerializedName("CreatedByNavigation") val createdByNavigation: UserNavigation?
)


data class UserNavigation(
    @SerializedName("Id") val id: Int,
    @SerializedName("Username") val username: String?,
    @SerializedName("Fullname") val fullname: String?,
)

/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity that shows how to use text to speech on a RealWear device.
 * This demo allows users to input text and have it spoken aloud using RealWear's TTS service.
 */
class TextToSpeechActivity : BaseActivity() {

    companion object {
        private const val TAG = "TextToSpeechActivity"
        
        // Intent actions for starting TTS and for being notified it finishes
        private const val ACTION_TTS = "com.realwear.ttsservice.intent.action.TTS"
        private const val ACTION_TTS_FINISHED = "com.realwear.ttsservice.intent.action.TTS_FINISHED"
        
        // Identifiers used when starting TTS commands
        private const val EXTRA_TEXT = "text_to_speak"
        private const val EXTRA_ID = "tts_id"
        private const val EXTRA_PAUSE = "pause_speech_recognizer"
        private const val TTS_REQUEST_CODE = 34
    }

    private lateinit var mTextInput: EditText
    private lateinit var mStatusView: TextView
    private lateinit var mHistoryView: TextView
    private lateinit var btnSpeakText: Button
    private lateinit var btnSpeakSample: Button
    private lateinit var btnClearHistory: Button
    private lateinit var btnBack: Button
    
    private var ttsRequestCount = 0
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_text_to_speech)

        initializeViews()
        updateStatus(getString(R.string.realwear_tts_ready))
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mTextInput = findViewById(R.id.textInput)
        mStatusView = findViewById(R.id.statusView)
        mHistoryView = findViewById(R.id.historyView)
        btnSpeakText = findViewById(R.id.btnSpeakText)
        btnSpeakSample = findViewById(R.id.btnSpeakSample)
        btnClearHistory = findViewById(R.id.btnClearHistory)
        btnBack = findViewById(R.id.btnBack)

        // Set initial values
        mHistoryView.text = getString(R.string.realwear_tts_history_empty)
        mTextInput.hint = getString(R.string.realwear_tts_input_hint)
        
        // Setup click listeners
        btnSpeakText.setOnClickListener {
            val inputText = mTextInput.text.toString().trim()
            if (inputText.isNotEmpty()) {
                speakText(inputText, "Custom Text")
            } else {
                Toast.makeText(this, R.string.realwear_tts_empty_text, Toast.LENGTH_SHORT).show()
                updateStatus(getString(R.string.realwear_tts_empty_text_status))
            }
        }
        
        btnSpeakSample.setOnClickListener {
            val sampleText = getString(R.string.realwear_tts_sample_text)
            speakText(sampleText, "Sample Text")
        }
        
        btnClearHistory.setOnClickListener {
            clearHistory()
        }
        
        btnBack.setOnClickListener {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        
        try {
            // Register broadcast receiver for TTS finished events
            val filter = IntentFilter().apply {
                addAction(ACTION_TTS_FINISHED)
            }
            registerReceiver(ttsReceiver, filter)
            Log.d(TAG, "TTS broadcast receiver registered")
            updateStatus(getString(R.string.realwear_tts_service_ready))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register TTS receiver", e)
            Toast.makeText(this, R.string.realwear_tts_register_failed, Toast.LENGTH_LONG).show()
        }
    }

    override fun onPause() {
        super.onPause()
        
        try {
            // Unregister broadcast receiver to prevent memory leaks
            unregisterReceiver(ttsReceiver)
            Log.d(TAG, "TTS broadcast receiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unregister TTS receiver", e)
        }
    }

    /**
     * Start TTS with the specified text
     */
    private fun speakText(textToSpeak: String, textType: String) {
        try {
            ttsRequestCount++
            val currentRequestId = TTS_REQUEST_CODE + ttsRequestCount
            
            Log.d(TAG, "Starting TTS for: $textToSpeak")
            
            val intent = Intent(ACTION_TTS).apply {
                putExtra(EXTRA_TEXT, textToSpeak)
                putExtra(EXTRA_ID, currentRequestId)
                putExtra(EXTRA_PAUSE, false) // Don't pause speech recognizer while TTS is playing
            }
            
            sendBroadcast(intent)
            
            // Update UI
            addToHistory(textType, textToSpeak, currentRequestId)
            updateStatus(getString(R.string.realwear_tts_speaking, textToSpeak.take(30)))
            
            Log.d(TAG, "TTS broadcast sent with ID: $currentRequestId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start TTS", e)
            updateStatus(getString(R.string.realwear_tts_start_failed))
            Toast.makeText(this, R.string.realwear_tts_start_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Add TTS request to history
     */
    private fun addToHistory(textType: String, textToSpeak: String, requestId: Int) {
        val timestamp = dateFormatter.format(Date())
        val currentHistory = mHistoryView.text.toString()
        val newEntry = "[$timestamp] $textType (ID: $requestId):\n\"$textToSpeak\"\n"
        
        val updatedHistory = if (currentHistory == getString(R.string.realwear_tts_history_empty)) {
            newEntry
        } else {
            "$currentHistory\n$newEntry"
        }
        
        mHistoryView.text = updatedHistory
    }

    /**
     * Clear all history and reset the display
     */
    private fun clearHistory() {
        ttsRequestCount = 0
        mHistoryView.text = getString(R.string.realwear_tts_history_empty)
        updateStatus(getString(R.string.realwear_tts_history_cleared))
        
        Toast.makeText(this, R.string.realwear_tts_history_cleared, Toast.LENGTH_SHORT).show()
        Log.d(TAG, "TTS history cleared")
    }

    /**
     * Update the status display
     */
    private fun updateStatus(status: String) {
        mStatusView.text = getString(R.string.realwear_tts_status_label, status)
    }

    /**
     * Broadcast receiver for being notified when TTS is complete
     */
    private val ttsReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            try {
                if (intent?.action == ACTION_TTS_FINISHED) {
                    val ttsID = intent.getIntExtra(EXTRA_ID, 0)
                    Log.d(TAG, "TTS finished, ID: $ttsID")
                    
                    updateStatus(getString(R.string.realwear_tts_finished, ttsID))
                    
                    // Update history to mark completion
                    val currentHistory = mHistoryView.text.toString()
                    val updatedHistory = currentHistory.replace(
                        "ID: $ttsID):",
                        "ID: $ttsID) ✓ Completed:"
                    )
                    mHistoryView.text = updatedHistory
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing TTS finished event", e)
                updateStatus(getString(R.string.realwear_tts_processing_error))
            }
        }
    }
}

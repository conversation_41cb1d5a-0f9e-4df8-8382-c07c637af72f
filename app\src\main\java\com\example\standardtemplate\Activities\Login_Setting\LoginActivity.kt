package com.example.standardtemplate.Activities.Login_Setting

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.LocaleListCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog.showSettingDialog
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.Activities.Register.RegisterActivity
import com.example.standardtemplate.Activities.RealWear.RealWearMainActivity
import com.example.standardtemplate.Dialogs.ConfirmationDialog.showConfirmDialog
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Models.FcmTokenRequest
import com.example.standardtemplate.Models.LoginInfo
import com.example.standardtemplate.Models.LoginResponse
import com.example.standardtemplate.Models.UserDetails
import com.example.standardtemplate.R
import com.google.firebase.messaging.FirebaseMessaging
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import kotlin.system.exitProcess

class LoginActivity : BaseActivity() {
    private lateinit var txtUsername: EditText
    private lateinit var txtPass: EditText
    private lateinit var btnLogin: Button
    private lateinit var btnSetting: Button
    private lateinit var btnLanguage: Button
    private lateinit var btnRealWearDev: Button
    private lateinit var hlRegister: TextView
    private lateinit var apiService: ApiInterface
    private lateinit var spinner: Spinner

    //SharedPref
    private lateinit var sharedPref: SharedPref

    private var lastVisit: String? = null
    private val REQUEST_PERMISSIONS = 1001
    private var deviceToken : String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_login)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        sharedPref = SharedPref(this)

        //start FCM
        val server = sharedPref.getBaseUrl(this@LoginActivity).toString()
        //check if the baseURL is not empty
        if (server.isNotEmpty()){
            //if baseurl not empty, then subscribe to the ticket_updates topic and send device token to server for storage.
            registerFCMToken()
            sendFcmToken()
            lastVisit = sharedPref.getLastVisitRecord(this@LoginActivity) // get last visit form sharedPref
            startMonitoringService(this@LoginActivity,lastVisit.toString()) // start backend monitoring
            Log.i("TicketMonitoring", "Service start")
        }else{
            Toast.makeText(this@LoginActivity, "Please register server URL!", Toast.LENGTH_SHORT).show() //show toast if no baseURL contain
        }
        //end FCM
        maybeRequestBatteryWhitelist()
        checkPermission()

        //declaration of the component
        txtUsername = findViewById(R.id.txtUsername)
        txtPass = findViewById(R.id.txtPass)
        btnLogin = findViewById(R.id.btnLogin)
        btnSetting = findViewById(R.id.btnSetting)
        btnRealWearDev = findViewById(R.id.btnRealWearDev)
        hlRegister = findViewById(R.id.hlRegister)
//        btnLanguage = findViewById(R.id.btnLanguage)
        spinner = findViewById(R.id.spinner)

        //start spinner drop down list for language
        val language = sharedPref.getLanguage(this@LoginActivity)
        val spinnerItem: List<String>
        val languageCodeMap: Map<String, String>

        if (language == "zh") {
            spinnerItem = listOf("英文", "中文")
            languageCodeMap = mapOf("英文" to "en", "中文" to "zh")
        } else {
            spinnerItem = listOf("English", "Chinese")
            languageCodeMap = mapOf("English" to "en", "Chinese" to "zh")
        }

        val spinnerAdapter = ArrayAdapter(this, R.layout.spinner_item, spinnerItem)
        spinner.adapter = spinnerAdapter

        // Set selected item based on saved language
        val selectedIndex = when (language) {
            "zh" -> spinnerItem.indexOf("中文")
            "en" -> spinnerItem.indexOf("English")
            else -> 0
        }
        spinner.setSelection(selectedIndex)

        // Prevent onItemSelected from triggering on setup
        var isFirstSelection = true

        spinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>,
                view: View?,
                position: Int,
                id: Long
            ) {
                if (isFirstSelection) {
                    isFirstSelection = false
                    return
                }

                val selectedText = spinnerItem[position]
                val selectedLangCode = languageCodeMap[selectedText]

                // Only change if different from current
                if (selectedLangCode != null && selectedLangCode != language) {
                    changeLanguage(selectedLangCode)
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        //end spinner drop down list for language

        btnLogin.setOnClickListener {
            val username = txtUsername.text.toString()
            val password = txtPass.text.toString()

            if (username.isEmpty()) {
                showMessageDialog(
                    this@LoginActivity,
                    getString(R.string.L_002),
                    getString(R.string.errorCode_L_002)
                )
            }
            if (password.isEmpty()) {
                showMessageDialog(
                    this@LoginActivity,
                    getString(R.string.L_003),
                    getString(R.string.errorCode_L_003)
                )
            }
            if (username.isNotEmpty() && password.isNotEmpty()) {
                login(username, password)//call function by passing the username and password
            } else {
                showMessageDialog(
                    this,
                    getString(R.string.reminder),
                    getString(R.string.reminderMsg)
                )
                return@setOnClickListener
            }
        }

        /*
        btn Setting --> used to handle setting baseUrl request
        user need to insert the correct url and click on the btnSave in order to save the baseUrl into the sharedPreferences so that the baseUrl can be used in the future
         */
        btnSetting.setOnClickListener {
            showSettingDialog(this)
        }

        /*
        hlRegister --> used to navigate to the register activity when user click on the text
         */
        hlRegister.setOnClickListener {
            val intent = Intent(this, RegisterActivity::class.java)
            startActivity(intent)
        }

        /*
        btnRealWearDev --> RealWear开发者直接入口
        绕过登录系统，直接进入RealWear功能测试界面
        专为RealWear智能眼镜设备开发和测试优化
         */
        btnRealWearDev.setOnClickListener {
            val intent = Intent(this, RealWearMainActivity::class.java)
            startActivity(intent)
        }

        /*
        handle onBackPress function
        prompt out a logout confirmation dialog for the user when onBackPressed is triggered.
        after the user logout, store the specific lastVisit record into the sharedPref 'ticketMonitoring', and clear all the sharedPref in 'AuthPrefs'
        then stop the monitoring service and navigate to the login activity
         */
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                showConfirmDialog(
                    this@LoginActivity,
                    getString(R.string.exit),
                    getString(R.string.exitMsg),
                    onConfirm = {
                        finishAffinity() // Closes all activities in the app
                        exitProcess(0)   // Ensures the app process is killed
                    })
            }
        })

    }

    /**
     *  registerFCMToken --> register the device token to subscribe to topic ticket_updates
     */
    private fun registerFCMToken() {
        FirebaseMessaging.getInstance().subscribeToTopic("ticket_updates")
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Log.d("FCM", "Subscribed to topic successfully")
                }
                else{
                    Log.w("FCM", "Subscription to topic failed", task.exception)
                }
            }
    }

    /**
     *  sendFcmToken --> send the device token to server for storage
     */
    private fun sendFcmToken() {
        if (sharedPref.getBaseUrl(this).isNotEmpty()){
            FirebaseMessaging.getInstance().token
                .addOnCompleteListener { task -> //fetching device token
                    if (!task.isSuccessful) {// if device token failed to get
                        Log.w("FCM", "Fetching FCM token failed", task.exception)
                        task.exception?.printStackTrace()
                        return@addOnCompleteListener
                    }

                    if (task.result.isNullOrEmpty()) { // check deviceToken is null or not
                        Log.w("FCM", "FCM token is null or empty")
                        return@addOnCompleteListener
                    }
                    deviceToken = task.result //store deviceToken result into variable deviceToken
                    Log.d("FCM", "Retrieved FCM token: $deviceToken")

                    val tokenReq = FcmTokenRequest(deviceToken!!, "ticket_updates") //add the token into the request body

                    val apiService = ApiClient.getUnauthenticatedClient(this).create(ApiInterface::class.java)
                    apiService.sendToken(tokenReq).enqueue(object : Callback<Void> { // send the token to server
                        override fun onResponse(call: Call<Void>, response: Response<Void>) {
                            if (response.isSuccessful) {
                                Log.i("FCM", "Token sent successfully to server")
                            } else {
                                Log.e("FCM", "Server error sending token: ${response.errorBody()?.string()}")
                                showMessageDialog(this@LoginActivity, "${getString(R.string.F_001)}", "${getString(R.string.errorCode_F_001)}")
                                return
                            }
                        }

                        override fun onFailure(call: Call<Void>, t: Throwable) {
                            Log.e("Network Error", "Network Error: ${t.message}")
                            Toast.makeText(
                                applicationContext,
                                "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    })
                }
        }else{
            Toast.makeText(this@LoginActivity, "Url Empty", Toast.LENGTH_SHORT).show()
            return
        }

    }
    //end FCM function

    /**
     *  changeLanguage --> change the language of the app
     *  @param languageCode the language code of the language that user selected, passed from the btnLanguage
     *  @return restart the application and set the locale of the application to the languageCode selected
     */
    private fun changeLanguage(languageCode: String) {
        var language = languageCode
        if (language.equals("en")) {
            language = "English"
        } else {
            language = "中文"
        }
        //1. onclick, prompt dialog ask whether to change, and say restart app is required
        showConfirmDialog(
            this@LoginActivity,
            getString(R.string.change_language),
            getString(R.string.change_language_content1) + " " + language + "? \n" + getString(R.string.change_language_content2)
        ) {
            //2. ok -> restart app + kill current app
            val intent = Intent(applicationContext, LoginActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            sharedPref.saveLanugage(this@LoginActivity, languageCode) //set language to the sharedPref

            // Update the global locale in the Application class
            (application as StandardTemplate).setLocale()
            //Set the locale globally using AppCompatDelegate
            AppCompatDelegate.setApplicationLocales(LocaleListCompat.forLanguageTags(languageCode))

            startActivity(intent)
            finish()
        }
    }

    /**
     *  login function --> allow the user to login with username and password
     * @param username the username of the user, passed from the txtUsername that user entered
     * @param password the password of the user, passed from the txtPass that user entered
     * @return call the login function from the api and verified the user information, return token and expiration
     */
    private fun login(username: String, password: String) {
        ApiClient.clearVals()
        apiService = ApiClient.getUnauthenticatedClient(this).create(ApiInterface::class.java)
        val loginInfo = LoginInfo(username, password)

        apiService.Login(loginInfo).enqueue(object : Callback<LoginResponse> {
            override fun onResponse(call: Call<LoginResponse>, response: Response<LoginResponse>) {
                if (response.isSuccessful && response.body() != null) {
                    val authentication =
                        response.body() // store the response's content into variable authentication
                    //save token to the userInfoManager
                    if (authentication != null) {
                        UserInfoManager.setAuthentication(authentication)

                        getUserData(authentication.token)
                        showMessageDialog(
                            this@LoginActivity,
                            getString(R.string.success),
                            "$username, " + getString(R.string.welcMsg)
                        ) {
                            //start ticketMonitoring
                            startMonitoringService(this@LoginActivity, lastVisit?:"")
                            val intent =
                                Intent(applicationContext, NewTicketListActivity::class.java)
                            startActivity(intent)
                            finish()
                        }
                    }
                } else {
                    showMessageDialog(
                        this@LoginActivity,
                        getString(R.string.L_001),
                        getString(R.string.errorCode_L_001)
                    )
                }
            }

            //handle network error
            override fun onFailure(call: Call<LoginResponse>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(
                    applicationContext,
                    "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        })
    }

    /**
     *  getUserData --> get the user information from the api
     *  @param token the token of the user, passed from the login function
     *  @return user information that include username, id, fullname
     */
    private fun getUserData(token: String) {
        apiService = ApiClient.getAuthenticatedClient(this, token).create(ApiInterface::class.java)

        apiService.getUserInfo().enqueue(object : Callback<UserDetails> {
            override fun onResponse(call: Call<UserDetails>, response: Response<UserDetails>) {
                if (response.isSuccessful && response.body() != null) {
                    val user = response.body()!!
                    //save userDetails into the userInfoManager
                    UserInfoManager.setUserData(user)
                } else {
                    showMessageDialog(
                        applicationContext,
                        getString(R.string.U_001),
                        getString(R.string.errorCode_U_001)
                    )
                    Log.w("USER INFO", "Failed to get user info")
                }
            }

            override fun onFailure(call: Call<UserDetails>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(
                    applicationContext,
                    "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        })
    }

    /*
    checkPermission() --> used to check the permission gained from the device, if there is any missing permission, it will be requested
    the permission requested in this function got POST_NOTIFICATION, FOREGROUND_SERVICE, LOCATION_PERMISSION, and POWER_SERVICE
     */
    @SuppressLint("BatteryLife")
    private fun checkPermission() {
        val permissionsToRequest = mutableListOf<String>()

        // 🔹 POST_NOTIFICATIONS (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissionsToRequest.add(Manifest.permission.POST_NOTIFICATIONS)
        }

        // 🔹 Foreground Service Permission (Android 14+ requires explicit request)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE &&
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.FOREGROUND_SERVICE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissionsToRequest.add(Manifest.permission.FOREGROUND_SERVICE)
        }

        // 🔹 Location Permissions (if needed for features)
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissionsToRequest.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }

        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissionsToRequest.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        // 🔹 Request runtime permissions
        if (permissionsToRequest.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                this,
                permissionsToRequest.toTypedArray(),
                REQUEST_PERMISSIONS
            )
        }

        // 🔹 Check for Battery Optimization (this is NOT a runtime permission, but a system setting)
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val packageName = packageName
            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:$packageName")
                }
                startActivity(intent)
            }
        }
    }

    //ask to make the app whitelist from the optimization
    private fun maybeRequestBatteryWhitelist() {
        val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
        val pkg = packageName
        if (!pm.isIgnoringBatteryOptimizations(pkg)) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                .setData(Uri.parse("package:$pkg"))
            startActivity(intent)
        }
    }
}
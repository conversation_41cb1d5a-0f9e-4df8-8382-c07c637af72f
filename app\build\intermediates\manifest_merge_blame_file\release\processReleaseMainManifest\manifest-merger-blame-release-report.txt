1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.standardtemplate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10    <!-- Permissions for network access -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:5-76
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:22-73
14    <!-- Foreground service permissions -->
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:5-87
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:22-84
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:5-86
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:22-83
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:22-78
21    <!-- background permission -->
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:5-95
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:22-92
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:5-68
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:22-65
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:5-81
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:5-92
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:22-89
26    <!-- storage permission -->
27    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:5-80
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:22-77
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:5-81
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:22-78
29    <!-- Camera permissions for RealWear functionality -->
30    <uses-permission android:name="android.permission.CAMERA" />
30-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:5-65
30-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:22-62
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:5-71
31-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:22-68
32
33    <uses-feature
33-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:5-85
34        android:name="android.hardware.camera"
34-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:19-57
35        android:required="false" />
35-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:58-82
36    <uses-feature
36-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:5-95
37        android:name="android.hardware.camera.autofocus"
37-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:19-67
38        android:required="false" />
38-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:68-92
39
40    <!-- Required by older versions of Google Play services to create IID tokens -->
41    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
41-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:5-82
41-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:22-79
42    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
42-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
42-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:22-76
44
45    <permission
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:29:5-159:19
52        android:name="com.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate"
52-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:30:9-67
53        android:allowBackup="true"
53-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:31:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:32:9-65
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:33:9-54
58        android:icon="@mipmap/ic_launcher"
58-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:34:9-43
59        android:label="@string/app_name"
59-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:35:9-41
60        android:requestLegacyExternalStorage="true"
60-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:36:9-52
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:37:9-54
62        android:supportsRtl="true"
62-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:38:9-35
63        android:theme="@style/Theme.StandardTemplate"
63-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:39:9-54
64        android:usesCleartextTraffic="true" >
64-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:40:9-44
65        <activity
65-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:42:9-44:40
66            android:name="com.example.standardtemplate.Activities.Register.RegisterActivity"
66-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:43:13-65
67            android:exported="false" />
67-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:44:13-37
68        <activity
68-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:45:9-47:40
69            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity"
69-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:46:13-81
70            android:exported="false" />
70-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:47:13-37
71        <activity
71-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:48:9-50:40
72            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity"
72-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:49:13-84
73            android:exported="false" />
73-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:50:13-37
74        <activity
74-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:51:9-53:40
75            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity"
75-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:52:13-75
76            android:exported="false" />
76-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:53:13-37
77        <activity
77-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:54:9-56:40
78            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity"
78-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:55:13-72
79            android:exported="false" />
79-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:13-37
80        <activity
80-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:57:9-65:20
81            android:name="com.example.standardtemplate.Activities.Login_Setting.LoginActivity"
81-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:13-67
82            android:exported="true" >
82-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:59:13-36
83            <intent-filter>
83-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:60:13-64:29
84                <action android:name="android.intent.action.MAIN" />
84-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:61:17-69
84-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:61:25-66
85
86                <category android:name="android.intent.category.LAUNCHER" />
86-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:17-77
86-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:27-74
87            </intent-filter>
88        </activity>
89        <!-- RealWear功能相关Activity -->
90        <activity
90-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:67:9-69:40
91            android:name="com.example.standardtemplate.Activities.RealWear.RealWearMainActivity"
91-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:68:13-69
92            android:exported="false" />
92-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:69:13-37
93        <activity
93-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:70:9-74:77
94            android:name="com.example.standardtemplate.Activities.RealWear.ActionButtonActivity"
94-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:71:13-69
95            android:configChanges="orientation|keyboardHidden|screenSize"
95-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:74:13-74
96            android:exported="false"
96-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:72:13-37
97            android:screenOrientation="landscape" />
97-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:73:13-50
98        <activity
98-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:9-79:77
99            android:name="com.example.standardtemplate.Activities.RealWear.CameraAppletActivity"
99-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:76:13-69
100            android:configChanges="orientation|keyboardHidden|screenSize"
100-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:79:13-74
101            android:exported="false"
101-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:77:13-37
102            android:screenOrientation="landscape" />
102-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:78:13-50
103        <activity
103-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:80:9-84:77
104            android:name="com.example.standardtemplate.Activities.RealWear.DocumentAppletActivity"
104-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:81:13-71
105            android:configChanges="orientation|keyboardHidden|screenSize"
105-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:84:13-74
106            android:exported="false"
106-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:82:13-37
107            android:screenOrientation="landscape" />
107-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:83:13-50
108        <activity
108-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:85:9-89:77
109            android:name="com.example.standardtemplate.Activities.RealWear.MovieAppletActivity"
109-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:86:13-68
110            android:configChanges="orientation|keyboardHidden|screenSize"
110-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:89:13-74
111            android:exported="false"
111-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:87:13-37
112            android:screenOrientation="landscape" />
112-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:88:13-50
113        <activity
113-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:90:9-94:77
114            android:name="com.example.standardtemplate.Activities.RealWear.BarcodeAppletActivity"
114-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:91:13-70
115            android:configChanges="orientation|keyboardHidden|screenSize"
115-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:94:13-74
116            android:exported="false"
116-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:92:13-37
117            android:screenOrientation="landscape" />
117-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:93:13-50
118        <activity
118-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:95:9-100:58
119            android:name="com.example.standardtemplate.Activities.RealWear.KeyboardDictationActivity"
119-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:96:13-74
120            android:configChanges="orientation|keyboardHidden|screenSize"
120-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:99:13-74
121            android:exported="false"
121-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:97:13-37
122            android:screenOrientation="landscape"
122-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:98:13-50
123            android:windowSoftInputMode="adjustResize" />
123-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:100:13-55
124        <activity
124-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:101:9-105:77
125            android:name="com.example.standardtemplate.Activities.RealWear.SpeechRecognizerActivity"
125-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:102:13-73
126            android:configChanges="orientation|keyboardHidden|screenSize"
126-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:105:13-74
127            android:exported="false"
127-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:103:13-37
128            android:screenOrientation="landscape" />
128-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:104:13-50
129        <activity
129-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:106:9-110:77
130            android:name="com.example.standardtemplate.Activities.RealWear.TextToSpeechActivity"
130-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:107:13-69
131            android:configChanges="orientation|keyboardHidden|screenSize"
131-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:110:13-74
132            android:exported="false"
132-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:108:13-37
133            android:screenOrientation="landscape" />
133-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:109:13-50
134        <activity
134-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:111:9-115:77
135            android:name="com.example.standardtemplate.Activities.RealWear.MicrophoneReleaseActivity"
135-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:112:13-74
136            android:configChanges="orientation|keyboardHidden|screenSize"
136-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:115:13-74
137            android:exported="false"
137-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:113:13-37
138            android:screenOrientation="landscape" />
138-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:114:13-50
139        <activity
139-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:116:9-120:77
140            android:name="com.example.standardtemplate.Activities.RealWear.AudioCaptureActivity"
140-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:117:13-69
141            android:configChanges="orientation|keyboardHidden|screenSize"
141-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:120:13-74
142            android:exported="false"
142-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:118:13-37
143            android:screenOrientation="landscape" />
143-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:119:13-50
144        <activity
144-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:121:9-125:77
145            android:name="com.example.standardtemplate.Activities.RealWear.HelpMenuActivity"
145-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:122:13-65
146            android:configChanges="orientation|keyboardHidden|screenSize"
146-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:125:13-74
147            android:exported="false"
147-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:123:13-37
148            android:screenOrientation="landscape" />
148-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:124:13-50
149        <activity
149-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:126:9-130:77
150            android:name="com.example.standardtemplate.Activities.RealWear.BNFGrammarActivity"
150-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:127:13-67
151            android:configChanges="orientation|keyboardHidden|screenSize"
151-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:130:13-74
152            android:exported="false"
152-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:128:13-37
153            android:screenOrientation="landscape" />
153-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:129:13-50
154
155        <service
155-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:132:9-135:56
156            android:name="com.example.standardtemplate.Libraries.TicketMonitoring"
156-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:133:13-55
157            android:exported="false"
157-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:134:13-37
158            android:foregroundServiceType="dataSync" />
158-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:135:13-53
159        <service
159-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:136:9-139:61
160            android:name="com.example.standardtemplate.Libraries.NotificationService"
160-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:137:13-58
161            android:exported="false"
161-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:138:13-37
162            android:foregroundServiceType="mediaPlayback" />
162-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:139:13-58
163        <!-- Implement Firebase messaging service -->
164        <service
164-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:141:9-147:19
165            android:name="com.example.standardtemplate.Libraries.FirebaseMessagingService"
165-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:142:13-63
166            android:exported="false" >
166-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:143:13-37
167            <intent-filter>
167-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:144:13-146:29
168                <action android:name="com.google.firebase.MESSAGING_EVENT" />
168-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:17-78
168-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:25-75
169            </intent-filter>
170        </service>
171
172        <!-- FileProvider for secure file sharing (RealWear Document functionality) -->
173        <provider
174            android:name="androidx.core.content.FileProvider"
174-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:151:13-62
175            android:authorities="com.example.standardtemplate.fileprovider"
175-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:152:13-64
176            android:exported="false"
176-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:153:13-37
177            android:grantUriPermissions="true" >
177-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:154:13-47
178            <meta-data
178-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:155:13-157:63
179                android:name="android.support.FILE_PROVIDER_PATHS"
179-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:156:17-67
180                android:resource="@xml/file_provider_paths" />
180-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:157:17-60
181        </provider>
182
183        <receiver
183-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:31:9-38:20
184            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
184-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:32:13-78
185            android:exported="true"
185-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:33:13-36
186            android:permission="com.google.android.c2dm.permission.SEND" >
186-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:34:13-73
187            <intent-filter>
187-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:35:13-37:29
188                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
188-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:17-81
188-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:25-78
189            </intent-filter>
190        </receiver>
191        <!--
192             FirebaseMessagingService performs security checks at runtime,
193             but set to not exported to explicitly avoid allowing another app to call it.
194        -->
195        <service
195-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:44:9-51:19
196            android:name="com.google.firebase.messaging.FirebaseMessagingService"
196-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:45:13-82
197            android:directBootAware="true"
197-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:46:13-43
198            android:exported="false" >
198-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:47:13-37
199            <intent-filter android:priority="-500" >
199-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:144:13-146:29
200                <action android:name="com.google.firebase.MESSAGING_EVENT" />
200-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:17-78
200-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:25-75
201            </intent-filter>
202        </service>
203        <service
203-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:52:9-58:19
204            android:name="com.google.firebase.components.ComponentDiscoveryService"
204-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:53:13-84
205            android:directBootAware="true"
205-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:34:13-43
206            android:exported="false" >
206-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:54:13-37
207            <meta-data
207-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:55:13-57:85
208                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
208-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:56:17-119
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:57:17-82
210            <meta-data
210-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
211                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
211-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
213            <meta-data
213-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
214                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
214-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
216            <meta-data
216-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:17:13-19:85
217                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
217-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:18:17-127
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:19:17-82
219        </service>
220
221        <receiver
221-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
222            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
222-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
223            android:enabled="true"
223-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
224            android:exported="false" >
224-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
225        </receiver>
226
227        <service
227-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
228            android:name="com.google.android.gms.measurement.AppMeasurementService"
228-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
229            android:enabled="true"
229-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
230            android:exported="false" />
230-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
231        <service
231-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
232            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
232-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
233            android:enabled="true"
233-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
234            android:exported="false"
234-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
235            android:permission="android.permission.BIND_JOB_SERVICE" />
235-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
236
237        <activity
237-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
238            android:name="com.google.android.gms.common.api.GoogleApiActivity"
238-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
239            android:exported="false"
239-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
241
242        <provider
242-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:25:9-30:39
243            android:name="com.google.firebase.provider.FirebaseInitProvider"
243-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:26:13-77
244            android:authorities="com.example.standardtemplate.firebaseinitprovider"
244-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:27:13-72
245            android:directBootAware="true"
245-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:28:13-43
246            android:exported="false"
246-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:29:13-37
247            android:initOrder="100" />
247-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:30:13-36
248
249        <meta-data
249-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
250            android:name="com.google.android.gms.version"
250-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
251            android:value="@integer/google_play_services_version" />
251-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
252
253        <provider
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
254            android:name="androidx.startup.InitializationProvider"
254-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
255            android:authorities="com.example.standardtemplate.androidx-startup"
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
256            android:exported="false" >
256-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
257            <meta-data
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
258                android:name="androidx.emoji2.text.EmojiCompatInitializer"
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
259                android:value="androidx.startup" />
259-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
260            <meta-data
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
261                android:name="androidx.work.WorkManagerInitializer"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
262                android:value="androidx.startup" />
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
263            <meta-data
263-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
264                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
264-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
265                android:value="androidx.startup" />
265-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
266            <meta-data
266-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
267                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
267-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
268                android:value="androidx.startup" />
268-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
269        </provider>
270
271        <service
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
272            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
273            android:directBootAware="false"
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
274            android:enabled="@bool/enable_system_alarm_service_default"
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
275            android:exported="false" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
276        <service
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
277            android:name="androidx.work.impl.background.systemjob.SystemJobService"
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
278            android:directBootAware="false"
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
279            android:enabled="@bool/enable_system_job_service_default"
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
280            android:exported="true"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
281            android:permission="android.permission.BIND_JOB_SERVICE" />
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
282        <service
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
283            android:name="androidx.work.impl.foreground.SystemForegroundService"
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
284            android:directBootAware="false"
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
285            android:enabled="@bool/enable_system_foreground_service_default"
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
286            android:exported="false" />
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
287
288        <receiver
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
289            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
290            android:directBootAware="false"
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
291            android:enabled="true"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
292            android:exported="false" />
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
293        <receiver
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
294            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
295            android:directBootAware="false"
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
296            android:enabled="false"
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
297            android:exported="false" >
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
298            <intent-filter>
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
299                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
300                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
301            </intent-filter>
302        </receiver>
303        <receiver
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
304            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
305            android:directBootAware="false"
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
306            android:enabled="false"
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
307            android:exported="false" >
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
308            <intent-filter>
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
309                <action android:name="android.intent.action.BATTERY_OKAY" />
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
310                <action android:name="android.intent.action.BATTERY_LOW" />
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
311            </intent-filter>
312        </receiver>
313        <receiver
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
314            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
315            android:directBootAware="false"
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
316            android:enabled="false"
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
317            android:exported="false" >
317-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
318            <intent-filter>
318-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
319                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
320                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
321            </intent-filter>
322        </receiver>
323        <receiver
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
324            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
325            android:directBootAware="false"
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
326            android:enabled="false"
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
327            android:exported="false" >
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
328            <intent-filter>
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
329                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
330            </intent-filter>
331        </receiver>
332        <receiver
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
333            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
335            android:enabled="false"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
336            android:exported="false" >
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
337            <intent-filter>
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
338                <action android:name="android.intent.action.BOOT_COMPLETED" />
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
339                <action android:name="android.intent.action.TIME_SET" />
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
340                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
341            </intent-filter>
342        </receiver>
343        <receiver
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
344            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
346            android:enabled="@bool/enable_system_alarm_service_default"
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
347            android:exported="false" >
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
348            <intent-filter>
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
349                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
353            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
353-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
355            android:enabled="true"
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
356            android:exported="true"
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
357            android:permission="android.permission.DUMP" >
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
358            <intent-filter>
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
359                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
360            </intent-filter>
361        </receiver>
362
363        <service
363-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
364            android:name="androidx.room.MultiInstanceInvalidationService"
364-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
365            android:directBootAware="true"
365-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
366            android:exported="false" />
366-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
367
368        <receiver
368-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
369            android:name="androidx.profileinstaller.ProfileInstallReceiver"
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
370            android:directBootAware="false"
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
371            android:enabled="true"
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
372            android:exported="true"
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
373            android:permission="android.permission.DUMP" >
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
375                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
376            </intent-filter>
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
378                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
379            </intent-filter>
380            <intent-filter>
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
381                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
382            </intent-filter>
383            <intent-filter>
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
384                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
385            </intent-filter>
386        </receiver>
387
388        <service
388-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
389            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
389-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
390            android:exported="false" >
390-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
391            <meta-data
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
392                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
393                android:value="cct" />
393-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
394        </service>
395        <service
395-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
396            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
396-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
397            android:exported="false"
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
398            android:permission="android.permission.BIND_JOB_SERVICE" >
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
399        </service>
400
401        <receiver
401-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
402            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
402-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
403            android:exported="false" />
403-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
404    </application>
405
406</manifest>

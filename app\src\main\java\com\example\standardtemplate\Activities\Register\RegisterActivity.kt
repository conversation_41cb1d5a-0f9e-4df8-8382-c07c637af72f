package com.example.standardtemplate.Activities.Register

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.standardtemplate.Activities.Login_Setting.LoginActivity
import com.example.standardtemplate.R
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Models.RegisterInfo
import com.example.standardtemplate.Models.RegisterResponse
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class RegisterActivity : BaseActivity() {
    //initialize component
    private lateinit var txtFullName: EditText
    private lateinit var txtUsername: EditText
    private lateinit var txtPassword: EditText
    private lateinit var btnRegister: Button
    private lateinit var hlLogin: TextView

    //initialize api
    private lateinit var apiService : ApiInterface

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_register)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        txtUsername = findViewById(R.id.txtUsername)
        txtPassword = findViewById(R.id.txtPassword)
        txtFullName = findViewById(R.id.txtFullName)
        btnRegister = findViewById(R.id.btnRegister)
        hlLogin = findViewById(R.id.hlLogin)

        hlLogin.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }

        btnRegister.setOnClickListener {
            val username = txtUsername.text.toString().trim()
            val password = txtPassword.text.toString().trim()
            val fullname = txtFullName.text.toString()
            //validate the input
            if (username !="" && password != "" && fullname !=""){
                register(username, password, fullname) //pass the information to the register function
            }else{
                showMessageDialog(this,getString(R.string.reminder), getString(R.string.reminderMsg2))
                return@setOnClickListener

            }
        }

    }

    /**
     *  register function --> allow the user to register with username, password, and fullname
     *  @param username the username of the user, passed from the txtUsername that user entered
     *  @param password the password of the user, passed from the txtPass that user entered
     *  @param fullname the fullname of the user, passed from the txtFullName that user entered
     *  @return registered information in data class RegisterResponse
     */
    private fun register(username: String, password: String, fullname: String) {
        val registerInfo = RegisterInfo(username, password, fullname) //set the information into the data class: RegisterInfo
        apiService = ApiClient.getUnauthenticatedClient(this@RegisterActivity).create(ApiInterface::class.java)

        apiService.registerAccount(registerInfo).enqueue(object: Callback<RegisterResponse>{
            override fun onResponse(
                call: Call<RegisterResponse>,
                response: Response<RegisterResponse>
            ) {
                if (response.isSuccessful && response.body()!=null){
                    showMessageDialog(this@RegisterActivity, getString(R.string.success), getString(R.string.proceedLogin), onOkClicked = {
                        val intent = Intent(this@RegisterActivity, LoginActivity::class.java)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK // Ensures a clean back stack
                        startActivity(intent) // Start the LoginActivity after register an account
                        finish()
                    })
                }else{
                    showMessageDialog(this@RegisterActivity, getString(R.string.R_001), getString(R.string.errorCode_R_001))
                }
            }

            override fun onFailure(call: Call<RegisterResponse>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()            }
        })
    }
}
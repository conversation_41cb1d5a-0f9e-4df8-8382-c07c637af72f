package com.example.standardtemplate.Libraries

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.example.standardtemplate.Activities.Login_Setting.LoginActivity
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.R
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * Foreground service that continuously polls the server for new tickets and raises
 * high‑importance alerts even when the phone is asleep or in Doze.
 *
 *  • Service notification (channel: CHANNEL_SERVICE) – low importance, ongoing.
 *  • Ticket alerts       (channel: CHANNEL_ALERTS)  – high importance, heads‑up.
 */
class TicketMonitoring : Service() {
    private lateinit var sharedPref:SharedPref
    private val token = UserInfoManager.token.toString()

    companion object {
        // ***** Notification & polling constants *****
        const val CHANNEL_SERVICE = "ticket_monitor_service"
        const val CHANNEL_ALERTS = "ticket_alerts"
        private const val SERVICE_NOTIFICATION_ID = 1
        private const val DEFAULT_POLL_INTERVAL_SEC = 10L // <‑‑ tune here
    }

    private val scheduler: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()
    private var lastDetect: String = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
    private var lastVisit :String?=null

    /*──────────────────────────  Service lifecycle  ─────────────────────────*/
    override fun onCreate() {
        super.onCreate()
        sharedPref = SharedPref(this@TicketMonitoring)
        createNotificationChannels()

        val notification = buildServiceNotification("Monitoring for new tickets…")
        startForeground(SERVICE_NOTIFICATION_ID, notification)

        Log.d("TicketMonitoring", "Foreground service started in onCreate")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d("TicketMonitoring", "onStartCommand called")
        lastVisit = intent?.getStringExtra("LAST_VISIT")
        Log.d("TicketMonitoring", "lastVisit from intent: $lastVisit")

        // Make sure polling only starts once
        if (!scheduler.isShutdown) {
            startPolling()
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e("TicketMonitoring", "Service destroyed! Check why.")
        scheduler.shutdownNow()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * when application is removed from recent task list, restart the service
     */
    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        val restartServiceIntent = Intent(applicationContext, TicketMonitoring::class.java)
        restartServiceIntent.setPackage(packageName)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            applicationContext.startForegroundService(restartServiceIntent)
            Log.i("Restart service", "Service Restart")
        } else {
            applicationContext.startService(restartServiceIntent)
        }
    }

    /*──────────────────────────  Notification helpers  ──────────────────────*/
    /**
     * used to create notification channel and user can edit the session at setting
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) return
        val nm = getSystemService(NotificationManager::class.java)

        val serviceChannel = NotificationChannel(
            CHANNEL_SERVICE, "Ticket Monitor (ongoing)", NotificationManager.IMPORTANCE_LOW
        ).apply { setShowBadge(false) }

        val alertChannel = NotificationChannel(
            CHANNEL_ALERTS, "Ticket Alerts", NotificationManager.IMPORTANCE_HIGH
        ).apply { enableVibration(true) }

        nm.createNotificationChannels(listOf(serviceChannel, alertChannel))
    }

    /*
    build background notification
     */
    private fun buildServiceNotification(text: String): Notification =
        NotificationCompat.Builder(this, CHANNEL_SERVICE)
            .setContentTitle("StandardTemplate")
            .setContentText(text)
            .setSmallIcon(R.drawable.application_icon)
            .setOngoing(true)
            .setAutoCancel(true)
            .setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
            .build()

    /*──────────────────────────  Polling logic  ─────────────────────────────*/
    /*
    start the polling service
     */
    private fun startPolling() {
        scheduler.scheduleWithFixedDelay({
            try {
                checkForNewTickets()
            } catch (e: Exception) {
                Log.e("TicketMonitoring", "Polling error: ${e.message}", e)
            }
        }, 0, DEFAULT_POLL_INTERVAL_SEC, TimeUnit.SECONDS)
    }

    /**
     * check for new ticket every 10 sec
     */
    private fun checkForNewTickets() {
        // Ensure lastDetect has a valid initial value.
        if (lastDetect.isEmpty()) lastDetect = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        lastVisit = sharedPref.getLastVisitRecord(this@TicketMonitoring)

        val apiService = ApiClient.getUnauthenticatedClient(this).create(ApiInterface::class.java)
        apiService.checkLatestTicket(lastDetect).enqueue(object : Callback<Int> {
            override fun onResponse(call: Call<Int>, response: Response<Int>) {
                if(response.isSuccessful && response.body()!= null){
                    val newCount = response.body() ?: 0
                    Log.d("TicketMonitoring", "Found $newCount new tickets since $lastVisit")
                    if (newCount > 0) {
                        showTicketNotification(this@TicketMonitoring, newCount)
                        lastDetect = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
                        SharedPref(this@TicketMonitoring).recordLastVisit(this@TicketMonitoring, lastDetect)
                    }
                }
            }

            override fun onFailure(call: Call<Int>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /*──────────────────────────  Misc helpers  ──────────────────────────────*/

    //check username exist before bring the user to the ui
    private fun ticketTapIntent(context: Context): Intent {
        val username = UserInfoManager.username.toString()
        return if (username.isBlank()) {
            // user not logged-in → go to login screen
            Intent(context, LoginActivity::class.java)
        } else {
            // user logged-in → go straight to home / tickets
            Intent(context, NewTicketListActivity::class.java).apply {
                // e.g. ask HomeActivity to open the Tickets tab
                putExtra("navigateTo", "tickets")
            }
        }.apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
    }

    private fun showTicketNotification(context:Context, count:Int){
        val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val pendingTap = PendingIntent.getActivity(context, 0, ticketTapIntent(context) , PendingIntent.FLAG_IMMUTABLE)

        val builder = NotificationCompat.Builder(context, TicketMonitoring.CHANNEL_ALERTS)
            .setSmallIcon(R.drawable.application_icon) // your icon
            .setContentTitle("New Ticket Created")
            .setContentText("Check the app for details.")
            .setContentIntent(pendingTap)
            .setPriority(NotificationCompat.PRIORITY_HIGH)

        nm.notify(System.currentTimeMillis().toInt(),  builder.build())
    }

}



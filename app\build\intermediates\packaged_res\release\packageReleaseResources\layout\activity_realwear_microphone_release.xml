<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_description"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Control Options Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_options_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/input_field_background"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/realwear_microphone_hide_text"
                android:textSize="14sp"
                android:textColor="@color/black" />

            <Switch
                android:id="@+id/switchHideText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/realwear_microphone_custom_message"
                android:textSize="14sp"
                android:textColor="@color/black" />

            <Switch
                android:id="@+id/switchCustomMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnReleaseMicrophone"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_microphone_release"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnRestoreMicrophone"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_microphone_restore"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnClearHistory"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_microphone_clear_history"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnBack"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_microphone_back"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- Status Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_status_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_status_ready"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- History Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_history_title"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/input_field_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/historyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_microphone_history_empty"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- Instructions Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_instructions_title"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_microphone_instructions"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:lineSpacingExtra="2dp" />

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="20dp"
    android:background="@color/white"
    tools:context=".Activities.NewTickets.NewTicketListActivity">

    <!-- Title -->
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:text="@string/newTicketTitle"
        android:textAlignment="center"
        android:textColor="@color/primaryColor"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Create + Button (Below Title) -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCreate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:backgroundTint="@color/primaryColor"
        android:text="@string/btnCreate"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtTitle" />

    <!-- RecyclerView for Users Table -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvTicketList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:background="@color/white"
        android:overScrollMode="always"
        android:padding="16dp"
        android:scrollbarStyle="outsideInset"
        android:scrollbars="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnCreate"
        app:layout_constraintBottom_toTopOf="@id/btnAcceptedTicketList"
        />

    <!-- Accepted Ticket List Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAcceptedTicketList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:backgroundTint="@color/primaryDarkColor"
        android:text="@string/acceptedTicketList"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rvTicketList"
        app:layout_constraintBottom_toTopOf="@id/btnRealWear"/>

    <!-- RealWear Development Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnRealWear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:backgroundTint="#4CAF50"
        android:text="@string/realwear_main_button"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnAcceptedTicketList"
        app:layout_constraintBottom_toTopOf="@id/btnLogout"/>

    <!-- Logout Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLogout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:backgroundTint="@color/accentColor"
        android:text="@string/logout"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnAcceptedTicketList" />

</androidx.constraintlayout.widget.ConstraintLayout>
package com.example.standardtemplate.Activities.Login_Setting.Dialog

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.Window
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.Toast
import com.example.standardtemplate.Activities.Login_Setting.LoginActivity
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Models.FcmTokenRequest
import com.example.standardtemplate.R
import com.google.firebase.messaging.FirebaseMessaging
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

object SettingDialog {
    private lateinit var sharedPref :SharedPref
    private var deviceToken : String? = null

    /*
    display the setting dialog to the user to update the baseUrl
     */
    fun showSettingDialog(context:Context){
        sharedPref = SharedPref(context)

        val settingDialog = Dialog(context)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_setting, null)
        settingDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        settingDialog.setContentView(view)
        settingDialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val edturl = view.findViewById<EditText>(R.id.edtUrl)
        val btnSave = view.findViewById<Button>(R.id.btnSave)
        val btnClose = view.findViewById<ImageView>(R.id.btnClose)

        /*
        btnSave --> update the baseUrl into the sharedPreferences
         */
        btnSave.setOnClickListener {
            ApiClient.clearVals()
            val url = edturl.text.toString()
            if (url.isEmpty() || !url.startsWith("http") || !url.endsWith("/")) {
                edturl.error = context.getString(R.string.ConnectionError) //used to display error message when the field is empty and user clicked btnSave
                return@setOnClickListener
            }
            else{
                //save baseUrl to the sharedPreference
                sharedPref.saveBaseUrl(context, url)
                showMessageDialog(context,context.getString(R.string.success), context.getString(R.string.updConnection)+"\n$url"){ // restart activity after this dialog dismiss
                    val intent = Intent(context, LoginActivity::class.java) // replace with your entry activity
                    
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    context.startActivity(intent)
                    if (context is Activity) {
                        context.finish()
                    }
                }

                settingDialog.dismiss()
            }
        }

        //btnClose --> close the dialog
        btnClose.setOnClickListener {
            settingDialog.dismiss()
        }

        settingDialog.show()
    }
}
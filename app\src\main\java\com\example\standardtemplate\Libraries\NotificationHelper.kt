package com.example.standardtemplate.Libraries

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.R

class NotificationHelper(private val context: Context) {
    private val channelId = "message_channel"
    private val handlerThread = HandlerThread("NotificationThread").apply { start() }
    private val handler = Handler(handlerThread.looper)

    init {
        createNotificationChannel()
    }

    /**
     * Creates a notification channel for Android Oreo and above.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Message Notifications",
                NotificationManager.IMPORTANCE_HIGH
            )
            val manager = context.getSystemService(NotificationManager::class.java)
            manager.createNotificationChannel(channel)
        }
    }

    /**
     * Creates a notification with the provided title and message.
     */
    fun createNotification(title: String, message: String): Notification {
        val intent = Intent(context, NewTicketListActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_LOW) // Foreground notifications should have LOW priority
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()
    }

    /**
     * Sends a notification with the provided title and message.
     */
    fun sendNotificationWithTask(title: String, message: String) {
        handler.post {
            val notification = createNotification(title, message)
            val manager = NotificationManagerCompat.from(context)
            if (manager.areNotificationsEnabled()) {
                manager.notify(System.currentTimeMillis().toInt(), notification)
            } else {
                Log.w("NOTIFICATION", "Notifications are disabled for this app.")
            }
        }
    }

    fun cleanup() {
        handlerThread.quitSafely() // Stop handler thread to release resources
    }

}

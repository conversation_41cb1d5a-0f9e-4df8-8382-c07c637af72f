[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_accepted_ticket_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_accepted_ticket_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_dialog_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\dialog_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_help_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_help_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_spinner_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\spinner_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_register.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_new_ticket_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_new_ticket_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_keyboard_dictation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_keyboard_dictation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\xml_file_provider_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\xml\\file_provider_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_dialog_create.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\dialog_create.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_camera_applet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_camera_applet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_rounded_background_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\rounded_background_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_new_ticket_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_new_ticket_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_ic_noti_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\ic_noti_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_movie_applet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_movie_applet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_document_applet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_document_applet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_accepted_ticket_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_accepted_ticket_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_bnf_grammar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_bnf_grammar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_radio_button_unchecked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\radio_button_unchecked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_input_field_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\input_field_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\xml_locales_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\xml\\locales_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\ic_back.xml"}, {"merged": "com.example.standardtemplate.app-debug-46:/layout_activity_realwear_camera_applet.xml.flat", "source": "com.example.standardtemplate.app-main-48:/layout/activity_realwear_camera_applet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_item_accepted_ticket_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\item_accepted_ticket_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_barcode_applet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_barcode_applet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_microphone_release.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_microphone_release.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_recycle_view_new_ticket_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\recycle_view_new_ticket_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_action_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_action_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_dialog_confirmation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\dialog_confirmation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_audio_capture.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_audio_capture.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_dialog_setting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\dialog_setting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_text_to_speech.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_text_to_speech.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_radio_button_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\radio_button_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_application_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\application_icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-debug-46:\\layout_activity_realwear_speech_recognizer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.standardtemplate.app-main-48:\\layout\\activity_realwear_speech_recognizer.xml"}]
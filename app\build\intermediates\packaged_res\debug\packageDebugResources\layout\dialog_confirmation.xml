<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:padding="24dp"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="@drawable/dialog_background"
        android:elevation="8dp"
        android:gravity="center"
        android:layout_gravity="center">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/actionStr"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/primaryColor"
            android:gravity="center"
            android:paddingBottom="8dp"/>

        <TextView
            android:id="@+id/tvMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/confirmationStr"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center"
            android:paddingBottom="16dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- Cancel Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:backgroundTint="@color/primaryDarkColor"
                android:text="@string/cancel"
                android:textColor="@color/white"
                app:cornerRadius="8dp" />

            <!-- Submit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnConfirm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/confirm"
                android:textColor="@color/white"
                android:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>

package com.example.standardtemplate.Models

import com.google.gson.annotations.SerializedName

data class NewTicketListResponse(
    @SerializedName("Id") val id: Int,
    @SerializedName("Creator") val creator: String,
    @SerializedName("Status") val status: String,
    @SerializedName("CreatedAt") val createdAt: String,  // ISO String format
    @SerializedName("MachineId") val machineId: String,
    @SerializedName("ErrorMsg") val errorMsg: String,
    @SerializedName("MachineStatus") val machineStatus: String,
    @SerializedName("DowntimeStr") val machineDowntime: String
)

<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Camera Applet Demo Layout -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/white"
    tools:context=".Activities.RealWear.CameraAppletActivity">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_camera_applet_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_camera_applet_description"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:lineSpacingMultiplier="1.2" />

    <!-- Image Preview Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_preview_label"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <!-- Image View for captured photos -->
        <ImageView
            android:id="@+id/camera_image_view"
            android:layout_width="300dp"
            android:layout_height="200dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/rounded_background_light"
            android:scaleType="centerCrop"
            android:contentDescription="@string/realwear_camera_preview_description"
            android:src="@drawable/ic_close" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_preview_instruction"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center" />

    </LinearLayout>

    <!-- Camera Action Buttons Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="16dp">

        <!-- Photo Capture Buttons -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_photo_section"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btnBitmapPhoto"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/realwear_camera_bitmap_photo"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <Button
            android:id="@+id/btnFileProviderPhoto"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/realwear_camera_file_provider_photo"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:gravity="center" />

        <!-- Video Recording Buttons -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_camera_video_section"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btnBasicVideo"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/realwear_camera_basic_video"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <Button
            android:id="@+id/btnFileProviderVideo"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/realwear_camera_file_provider_video"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:gravity="center" />

        <!-- Back Button -->
        <Button
            android:id="@+id/btnBack"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/btnBack"
            android:textSize="16sp"
            android:background="@drawable/dialog_background"
            android:textColor="@color/black"
            android:drawableStart="@drawable/ic_back"
            android:drawablePadding="8dp"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>

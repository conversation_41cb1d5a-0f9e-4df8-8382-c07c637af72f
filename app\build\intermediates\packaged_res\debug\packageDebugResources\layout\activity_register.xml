<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activities.Register.RegisterActivity"
    android:padding="20dp"
    android:background="@color/white">

    <!-- Header -->
    <TextView
        android:id="@+id/txtHeader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/register"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/primaryColor"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingBottom="16dp"
        android:layout_marginTop="30dp"/>

    <!-- Full Name -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/fullNameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        app:boxStrokeColor="@color/primaryColor"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/hintColor"
        app:layout_constraintTop_toBottomOf="@id/txtHeader"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtFullName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/fullname"
            android:inputType="textPersonName"
            android:textColor="@color/textColor"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Username -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/usernameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        app:boxStrokeColor="@color/primaryColor"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/hintColor"
        app:layout_constraintTop_toBottomOf="@id/fullNameLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtUsername"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/username"
            android:inputType="text"
            android:textColor="@color/textColor"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        app:boxStrokeColor="@color/primaryColor"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/hintColor"
        app:layout_constraintTop_toBottomOf="@id/usernameLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/pass"
            android:inputType="textPassword"
            android:textColor="@color/textColor"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Register Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnRegister"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="24dp"
        android:backgroundTint="@color/primaryColor"
        android:text="@string/btnRegister"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintTop_toBottomOf="@id/passwordLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Login Hyperlink -->
    <TextView
        android:id="@+id/hlLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        android:text="@string/hlLogin"
        android:textAlignment="textEnd"
        android:textColor="@color/primaryColor"
        android:textSize="14sp"
        app:layout_constraintTop_toBottomOf="@id/btnRegister"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
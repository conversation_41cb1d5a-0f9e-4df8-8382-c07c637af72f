<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear功能主界面布局 - 优化滚动和适配 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".Activities.RealWear.RealWearMainActivity">

    <!-- 固定标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/primary_color"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="RealWear Development Center"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test and explore RealWear device capabilities integrated with the ticket management system"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:lineSpacingMultiplier="1.2" />

    </LinearLayout>

    <!-- 可滚动的功能按钮区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="16dp">

        <!-- Action Button功能按钮 -->
        <Button
            android:id="@+id/btnActionButton"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="🔘 RealWear Action Button Demo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Camera Applet功能按钮 -->
        <Button
            android:id="@+id/btnCameraApplet"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="📸 RealWear Camera Applet Demo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Document Viewer功能按钮 -->
        <Button
            android:id="@+id/btnDocumentViewer"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="📄 RealWear Document Applet Demo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Movie Applet功能按钮 -->
        <Button
            android:id="@+id/btnMovieApplet"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_movie_applet_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Barcode Applet功能按钮 -->
        <Button
            android:id="@+id/btnBarcodeApplet"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_barcode_applet_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Keyboard & Dictation功能按钮 -->
        <Button
            android:id="@+id/btnKeyboardDictation"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_keyboard_dictation_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Speech Recognition功能按钮 -->
        <Button
            android:id="@+id/btnSpeechRecognition"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_speech_recognizer_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Text to Speech功能按钮 -->
        <Button
            android:id="@+id/btnTextToSpeech"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_tts_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Microphone Release功能按钮 -->
        <Button
            android:id="@+id/btnMicrophoneRelease"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_microphone_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Audio Capture功能按钮 -->
        <Button
            android:id="@+id/btnAudioCapture"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="@string/realwear_audio_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- Help Menu功能按钮 -->
        <Button
            android:id="@+id/btnHelpMenu"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="🔧 RealWear Help Menu Demo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- BNF Grammar功能按钮 - 第12个功能！ -->
        <Button
            android:id="@+id/btnBNFGrammar"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="🔤 RealWear BNF Grammar Demo"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="24dp"
            android:drawableStart="@drawable/radio_button_unchecked"
            android:drawablePadding="12dp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp" />

        <!-- 状态信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/rounded_background_light"
            android:padding="16dp"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="📊 Status: RealWear Integration COMPLETE! ✅"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/primary_color"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🔗 Compatibility: Kotlin Implementation successful"
                android:textSize="12sp"
                android:textColor="@color/dark_gray"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🎯 All Features: Action ✓, Camera ✓, Document ✓, Movie ✓, Barcode ✓, Keyboard ✓, Speech ✓, TTS ✓, Microphone ✓, Audio ✓, Help ✓, BNF ✓"
                android:textSize="10sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:lineSpacingMultiplier="1.2" />

        </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- 固定底部按钮 -->
    <Button
        android:id="@+id/btnBackToMain"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="🔙 Back to Main System"
        android:textSize="16sp"
        android:background="@drawable/dialog_background"
        android:textColor="@color/black"
        android:drawableStart="@drawable/ic_back"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:layout_margin="16dp" />

</LinearLayout>


<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_description"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Sample Rate Selection -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_sample_rate_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <RadioButton
            android:id="@+id/rate8Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_8khz"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rate16Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_16khz"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rate44Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_44khz"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/rate48Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_48khz"
            android:textSize="14sp" />

    </RadioGroup>

    <!-- Channel Configuration -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_channels_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <RadioButton
            android:id="@+id/monoButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_mono"
            android:textSize="14sp" />

        <RadioButton
            android:id="@+id/stereoButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_audio_stereo"
            android:textSize="14sp" />

    </RadioGroup>

    <!-- File Information -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_output_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/fileTextLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_filename_placeholder"
        android:textSize="12sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/recordButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:text="@string/realwear_audio_record"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/playbackButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:text="@string/realwear_audio_playback"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_marginBottom="16dp"
        android:visibility="invisible" />

    <!-- Status Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_status_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/statusLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_status_ready"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Instructions Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_audio_instructions_title"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_audio_instructions"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- Back Button -->
    <Button
        android:id="@+id/btnBack"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="@string/realwear_audio_back"
        android:textSize="16sp"
        android:background="@drawable/input_field_background"
        android:textColor="@color/black" />

</LinearLayout>

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.standardtemplate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10    <!-- Permissions for network access -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:5-76
13-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:7:22-73
14    <!-- Foreground service permissions -->
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:5-87
16-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:10:22-84
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:5-86
18-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:12:22-83
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:14:22-78
21    <!-- background permission -->
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:5-95
22-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:16:22-92
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:5-68
23-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:17:22-65
24    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:5-81
24-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:18:22-78
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:5-92
25-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:19:22-89
26    <!-- storage permission -->
27    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:5-80
27-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:21:22-77
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:5-81
28-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:22:22-78
29    <!-- Camera permissions for RealWear functionality -->
30    <uses-permission android:name="android.permission.CAMERA" />
30-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:5-65
30-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:24:22-62
31    <uses-permission android:name="android.permission.RECORD_AUDIO" />
31-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:5-71
31-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:25:22-68
32
33    <uses-feature
33-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:5-85
34        android:name="android.hardware.camera"
34-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:19-57
35        android:required="false" />
35-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:26:58-82
36    <uses-feature
36-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:5-95
37        android:name="android.hardware.camera.autofocus"
37-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:19-67
38        android:required="false" />
38-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:27:68-92
39
40    <!-- Required by older versions of Google Play services to create IID tokens -->
41    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
41-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:5-82
41-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:28:22-79
42    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
42-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
42-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:25:22-76
44
45    <permission
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.example.standardtemplate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:29:5-159:19
52        android:name="com.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate"
52-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:30:9-67
53        android:allowBackup="true"
53-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:31:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:32:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:33:9-54
59        android:icon="@mipmap/ic_launcher"
59-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:34:9-43
60        android:label="@string/app_name"
60-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:35:9-41
61        android:requestLegacyExternalStorage="true"
61-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:36:9-52
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:37:9-54
63        android:supportsRtl="true"
63-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:38:9-35
64        android:testOnly="true"
65        android:theme="@style/Theme.StandardTemplate"
65-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:39:9-54
66        android:usesCleartextTraffic="true" >
66-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:40:9-44
67        <activity
67-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:42:9-44:40
68            android:name="com.example.standardtemplate.Activities.Register.RegisterActivity"
68-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:43:13-65
69            android:exported="false" />
69-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:44:13-37
70        <activity
70-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:45:9-47:40
71            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity"
71-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:46:13-81
72            android:exported="false" />
72-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:47:13-37
73        <activity
73-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:48:9-50:40
74            android:name="com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity"
74-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:49:13-84
75            android:exported="false" />
75-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:50:13-37
76        <activity
76-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:51:9-53:40
77            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity"
77-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:52:13-75
78            android:exported="false" />
78-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:53:13-37
79        <activity
79-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:54:9-56:40
80            android:name="com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity"
80-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:55:13-72
81            android:exported="false" />
81-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:56:13-37
82        <activity
82-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:57:9-65:20
83            android:name="com.example.standardtemplate.Activities.Login_Setting.LoginActivity"
83-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:58:13-67
84            android:exported="true" >
84-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:59:13-36
85            <intent-filter>
85-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:60:13-64:29
86                <action android:name="android.intent.action.MAIN" />
86-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:61:17-69
86-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:61:25-66
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:17-77
88-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:63:27-74
89            </intent-filter>
90        </activity>
91        <!-- RealWear功能相关Activity -->
92        <activity
92-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:67:9-69:40
93            android:name="com.example.standardtemplate.Activities.RealWear.RealWearMainActivity"
93-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:68:13-69
94            android:exported="false" />
94-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:69:13-37
95        <activity
95-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:70:9-74:77
96            android:name="com.example.standardtemplate.Activities.RealWear.ActionButtonActivity"
96-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:71:13-69
97            android:configChanges="orientation|keyboardHidden|screenSize"
97-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:74:13-74
98            android:exported="false"
98-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:72:13-37
99            android:screenOrientation="landscape" />
99-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:73:13-50
100        <activity
100-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:75:9-79:77
101            android:name="com.example.standardtemplate.Activities.RealWear.CameraAppletActivity"
101-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:76:13-69
102            android:configChanges="orientation|keyboardHidden|screenSize"
102-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:79:13-74
103            android:exported="false"
103-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:77:13-37
104            android:screenOrientation="landscape" />
104-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:78:13-50
105        <activity
105-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:80:9-84:77
106            android:name="com.example.standardtemplate.Activities.RealWear.DocumentAppletActivity"
106-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:81:13-71
107            android:configChanges="orientation|keyboardHidden|screenSize"
107-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:84:13-74
108            android:exported="false"
108-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:82:13-37
109            android:screenOrientation="landscape" />
109-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:83:13-50
110        <activity
110-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:85:9-89:77
111            android:name="com.example.standardtemplate.Activities.RealWear.MovieAppletActivity"
111-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:86:13-68
112            android:configChanges="orientation|keyboardHidden|screenSize"
112-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:89:13-74
113            android:exported="false"
113-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:87:13-37
114            android:screenOrientation="landscape" />
114-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:88:13-50
115        <activity
115-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:90:9-94:77
116            android:name="com.example.standardtemplate.Activities.RealWear.BarcodeAppletActivity"
116-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:91:13-70
117            android:configChanges="orientation|keyboardHidden|screenSize"
117-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:94:13-74
118            android:exported="false"
118-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:92:13-37
119            android:screenOrientation="landscape" />
119-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:93:13-50
120        <activity
120-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:95:9-100:58
121            android:name="com.example.standardtemplate.Activities.RealWear.KeyboardDictationActivity"
121-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:96:13-74
122            android:configChanges="orientation|keyboardHidden|screenSize"
122-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:99:13-74
123            android:exported="false"
123-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:97:13-37
124            android:screenOrientation="landscape"
124-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:98:13-50
125            android:windowSoftInputMode="adjustResize" />
125-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:100:13-55
126        <activity
126-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:101:9-105:77
127            android:name="com.example.standardtemplate.Activities.RealWear.SpeechRecognizerActivity"
127-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:102:13-73
128            android:configChanges="orientation|keyboardHidden|screenSize"
128-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:105:13-74
129            android:exported="false"
129-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:103:13-37
130            android:screenOrientation="landscape" />
130-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:104:13-50
131        <activity
131-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:106:9-110:77
132            android:name="com.example.standardtemplate.Activities.RealWear.TextToSpeechActivity"
132-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:107:13-69
133            android:configChanges="orientation|keyboardHidden|screenSize"
133-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:110:13-74
134            android:exported="false"
134-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:108:13-37
135            android:screenOrientation="landscape" />
135-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:109:13-50
136        <activity
136-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:111:9-115:77
137            android:name="com.example.standardtemplate.Activities.RealWear.MicrophoneReleaseActivity"
137-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:112:13-74
138            android:configChanges="orientation|keyboardHidden|screenSize"
138-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:115:13-74
139            android:exported="false"
139-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:113:13-37
140            android:screenOrientation="landscape" />
140-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:114:13-50
141        <activity
141-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:116:9-120:77
142            android:name="com.example.standardtemplate.Activities.RealWear.AudioCaptureActivity"
142-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:117:13-69
143            android:configChanges="orientation|keyboardHidden|screenSize"
143-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:120:13-74
144            android:exported="false"
144-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:118:13-37
145            android:screenOrientation="landscape" />
145-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:119:13-50
146        <activity
146-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:121:9-125:77
147            android:name="com.example.standardtemplate.Activities.RealWear.HelpMenuActivity"
147-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:122:13-65
148            android:configChanges="orientation|keyboardHidden|screenSize"
148-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:125:13-74
149            android:exported="false"
149-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:123:13-37
150            android:screenOrientation="landscape" />
150-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:124:13-50
151        <activity
151-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:126:9-130:77
152            android:name="com.example.standardtemplate.Activities.RealWear.BNFGrammarActivity"
152-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:127:13-67
153            android:configChanges="orientation|keyboardHidden|screenSize"
153-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:130:13-74
154            android:exported="false"
154-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:128:13-37
155            android:screenOrientation="landscape" />
155-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:129:13-50
156
157        <service
157-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:132:9-135:56
158            android:name="com.example.standardtemplate.Libraries.TicketMonitoring"
158-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:133:13-55
159            android:exported="false"
159-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:134:13-37
160            android:foregroundServiceType="dataSync" />
160-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:135:13-53
161        <service
161-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:136:9-139:61
162            android:name="com.example.standardtemplate.Libraries.NotificationService"
162-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:137:13-58
163            android:exported="false"
163-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:138:13-37
164            android:foregroundServiceType="mediaPlayback" />
164-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:139:13-58
165        <!-- Implement Firebase messaging service -->
166        <service
166-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:141:9-147:19
167            android:name="com.example.standardtemplate.Libraries.FirebaseMessagingService"
167-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:142:13-63
168            android:exported="false" >
168-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:143:13-37
169            <intent-filter>
169-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:144:13-146:29
170                <action android:name="com.google.firebase.MESSAGING_EVENT" />
170-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:17-78
170-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:25-75
171            </intent-filter>
172        </service>
173
174        <!-- FileProvider for secure file sharing (RealWear Document functionality) -->
175        <provider
176            android:name="androidx.core.content.FileProvider"
176-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:151:13-62
177            android:authorities="com.example.standardtemplate.fileprovider"
177-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:152:13-64
178            android:exported="false"
178-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:153:13-37
179            android:grantUriPermissions="true" >
179-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:154:13-47
180            <meta-data
180-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:155:13-157:63
181                android:name="android.support.FILE_PROVIDER_PATHS"
181-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:156:17-67
182                android:resource="@xml/file_provider_paths" />
182-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:157:17-60
183        </provider>
184
185        <receiver
185-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:31:9-38:20
186            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
186-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:32:13-78
187            android:exported="true"
187-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:33:13-36
188            android:permission="com.google.android.c2dm.permission.SEND" >
188-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:34:13-73
189            <intent-filter>
189-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:35:13-37:29
190                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
190-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:17-81
190-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:36:25-78
191            </intent-filter>
192        </receiver>
193        <!--
194             FirebaseMessagingService performs security checks at runtime,
195             but set to not exported to explicitly avoid allowing another app to call it.
196        -->
197        <service
197-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:44:9-51:19
198            android:name="com.google.firebase.messaging.FirebaseMessagingService"
198-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:45:13-82
199            android:directBootAware="true"
199-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:46:13-43
200            android:exported="false" >
200-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:47:13-37
201            <intent-filter android:priority="-500" >
201-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:144:13-146:29
202                <action android:name="com.google.firebase.MESSAGING_EVENT" />
202-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:17-78
202-->D:\MobileProject\standardtemplate-kotlin\app\src\main\AndroidManifest.xml:145:25-75
203            </intent-filter>
204        </service>
205        <service
205-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:52:9-58:19
206            android:name="com.google.firebase.components.ComponentDiscoveryService"
206-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:53:13-84
207            android:directBootAware="true"
207-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:34:13-43
208            android:exported="false" >
208-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:54:13-37
209            <meta-data
209-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:55:13-57:85
210                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
210-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:56:17-119
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\AndroidManifest.xml:57:17-82
212            <meta-data
212-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
213                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
213-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
215            <meta-data
215-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
216                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
216-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
218            <meta-data
218-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:17:13-19:85
219                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
219-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:18:17-127
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\AndroidManifest.xml:19:17-82
221        </service>
222
223        <receiver
223-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
224            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
224-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
225            android:enabled="true"
225-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
226            android:exported="false" >
226-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
227        </receiver>
228
229        <service
229-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
230            android:name="com.google.android.gms.measurement.AppMeasurementService"
230-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
231            android:enabled="true"
231-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
232            android:exported="false" />
232-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
233        <service
233-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
234            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
234-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
235            android:enabled="true"
235-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
236            android:exported="false"
236-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
237            android:permission="android.permission.BIND_JOB_SERVICE" />
237-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
238
239        <activity
239-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
240            android:name="com.google.android.gms.common.api.GoogleApiActivity"
240-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
241            android:exported="false"
241-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
242            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
242-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
243
244        <provider
244-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:25:9-30:39
245            android:name="com.google.firebase.provider.FirebaseInitProvider"
245-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:26:13-77
246            android:authorities="com.example.standardtemplate.firebaseinitprovider"
246-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:27:13-72
247            android:directBootAware="true"
247-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:28:13-43
248            android:exported="false"
248-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:29:13-37
249            android:initOrder="100" />
249-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\AndroidManifest.xml:30:13-36
250
251        <meta-data
251-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
252            android:name="com.google.android.gms.version"
252-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
253            android:value="@integer/google_play_services_version" />
253-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
254
255        <provider
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
256            android:name="androidx.startup.InitializationProvider"
256-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
257            android:authorities="com.example.standardtemplate.androidx-startup"
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
258            android:exported="false" >
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
259            <meta-data
259-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
260                android:name="androidx.emoji2.text.EmojiCompatInitializer"
260-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
261                android:value="androidx.startup" />
261-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
262            <meta-data
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
263                android:name="androidx.work.WorkManagerInitializer"
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
264                android:value="androidx.startup" />
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
265            <meta-data
265-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
266                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
266-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
267                android:value="androidx.startup" />
267-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
268            <meta-data
268-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
269                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
269-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
270                android:value="androidx.startup" />
270-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
271        </provider>
272
273        <service
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
274            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
275            android:directBootAware="false"
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
276            android:enabled="@bool/enable_system_alarm_service_default"
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
277            android:exported="false" />
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
278        <service
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
279            android:name="androidx.work.impl.background.systemjob.SystemJobService"
279-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
280            android:directBootAware="false"
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
281            android:enabled="@bool/enable_system_job_service_default"
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
282            android:exported="true"
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
283            android:permission="android.permission.BIND_JOB_SERVICE" />
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
284        <service
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
285            android:name="androidx.work.impl.foreground.SystemForegroundService"
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
286            android:directBootAware="false"
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
287            android:enabled="@bool/enable_system_foreground_service_default"
287-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
288            android:exported="false" />
288-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
289
290        <receiver
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
291            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
293            android:enabled="true"
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
294            android:exported="false" />
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
295        <receiver
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
296            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
298            android:enabled="false"
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
299            android:exported="false" >
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
300            <intent-filter>
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
301                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
302                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
303            </intent-filter>
304        </receiver>
305        <receiver
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
306            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
308            android:enabled="false"
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
309            android:exported="false" >
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
310            <intent-filter>
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
311                <action android:name="android.intent.action.BATTERY_OKAY" />
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
312                <action android:name="android.intent.action.BATTERY_LOW" />
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
313            </intent-filter>
314        </receiver>
315        <receiver
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
316            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
317            android:directBootAware="false"
317-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
318            android:enabled="false"
318-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
319            android:exported="false" >
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
320            <intent-filter>
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
321                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
322                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
323            </intent-filter>
324        </receiver>
325        <receiver
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
326            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
328            android:enabled="false"
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
329            android:exported="false" >
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
330            <intent-filter>
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
331                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
332            </intent-filter>
333        </receiver>
334        <receiver
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
335            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
336            android:directBootAware="false"
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
337            android:enabled="false"
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
338            android:exported="false" >
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
339            <intent-filter>
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
340                <action android:name="android.intent.action.BOOT_COMPLETED" />
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
341                <action android:name="android.intent.action.TIME_SET" />
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
342                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
343            </intent-filter>
344        </receiver>
345        <receiver
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
346            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
347            android:directBootAware="false"
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
348            android:enabled="@bool/enable_system_alarm_service_default"
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
349            android:exported="false" >
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
350            <intent-filter>
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
351                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
352            </intent-filter>
353        </receiver>
354        <receiver
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
355            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
356            android:directBootAware="false"
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
357            android:enabled="true"
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
358            android:exported="true"
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
359            android:permission="android.permission.DUMP" >
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
360            <intent-filter>
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
361                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
361-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
361-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
362            </intent-filter>
363        </receiver>
364
365        <service
365-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
366            android:name="androidx.room.MultiInstanceInvalidationService"
366-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
367            android:directBootAware="true"
367-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
368            android:exported="false" />
368-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
369
370        <receiver
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
371            android:name="androidx.profileinstaller.ProfileInstallReceiver"
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
372            android:directBootAware="false"
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
373            android:enabled="true"
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
374            android:exported="true"
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
375            android:permission="android.permission.DUMP" >
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
377                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
378            </intent-filter>
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
380                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
383                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
386                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
387            </intent-filter>
388        </receiver>
389
390        <service
390-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
391            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
391-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
392            android:exported="false" >
392-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
393            <meta-data
393-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
394                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
394-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
395                android:value="cct" />
395-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
396        </service>
397        <service
397-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
398            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
398-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
399            android:exported="false"
399-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
400            android:permission="android.permission.BIND_JOB_SERVICE" >
400-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
401        </service>
402
403        <receiver
403-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
404            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
404-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
405            android:exported="false" />
405-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
406    </application>
407
408</manifest>

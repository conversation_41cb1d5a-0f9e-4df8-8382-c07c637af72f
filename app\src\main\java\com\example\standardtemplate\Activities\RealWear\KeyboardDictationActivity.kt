package com.example.standardtemplate.Activities.RealWear

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R

/**
 * RealWear Keyboard and Dictation Activity
 * 
 * This activity demonstrates how to accept input from the user using either a keyboard 
 * or dictation on a RealWear HMT device. It provides two input methods:
 * 1. Voice dictation using RealWear's built-in speech-to-text capability
 * 2. Traditional software keyboard input for text entry
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * Features:
 * - Launch RealWear's voice dictation system
 * - Show/hide software keyboard for manual text entry
 * - Process dictated text results and display them
 * - Support for both input methods with seamless switching
 * - Real-time text editing and manipulation
 */
class KeyboardDictationActivity : BaseActivity() {
    
    companion object {
        // Request code identifying dictation events
        private const val DICTATION_REQUEST_CODE = 34
        
        // RealWear dictation intent action
        private const val ACTION_DICTATION = "com.realwear.keyboard.intent.action.DICTATION"
        
        // Extra key for dictation result
        private const val EXTRA_RESULT = "result"
    }
    
    // UI Components
    private lateinit var mTextField: EditText
    private lateinit var btnLaunchDictation: Button
    private lateinit var btnLaunchKeyboard: Button
    private lateinit var btnClearText: Button
    private lateinit var btnHideKeyboard: Button
    private lateinit var btnBack: Button
    private lateinit var txtInstructions: TextView
    private lateinit var txtStatus: TextView
    
    /**
     * Called when the activity is created
     * 
     * @param savedInstanceState See Android documentation
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set fullscreen mode for optimal RealWear device display
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_keyboard_dictation)
        
        // Initialize UI components
        initializeViews()
        
        // Setup click listeners
        setupClickListeners()
        
        // Setup back press handler
        setupBackPressedHandler()
        
        // Initialize status
        updateStatus(getString(R.string.realwear_keyboard_dictation_ready))
    }
    
    /**
     * Initialize all UI components
     */
    private fun initializeViews() {
        mTextField = findViewById(R.id.dictationField)
        btnLaunchDictation = findViewById(R.id.btnLaunchDictation)
        btnLaunchKeyboard = findViewById(R.id.btnLaunchKeyboard)
        btnClearText = findViewById(R.id.btnClearText)
        btnHideKeyboard = findViewById(R.id.btnHideKeyboard)
        btnBack = findViewById(R.id.btnBack)
        txtInstructions = findViewById(R.id.txtInstructions)
        txtStatus = findViewById(R.id.txtStatus)
    }
    
    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        // Launch RealWear voice dictation
        btnLaunchDictation.setOnClickListener { 
            launchDictation()
        }
        
        // Launch software keyboard
        btnLaunchKeyboard.setOnClickListener { 
            launchKeyboard()
        }
        
        // Clear all text from the input field
        btnClearText.setOnClickListener { 
            clearTextField()
        }
        
        // Hide the software keyboard
        btnHideKeyboard.setOnClickListener { 
            hideKeyboard()
        }
        
        btnBack.setOnClickListener { finish() }
    }
    
    /**
     * Setup back press handler to maintain consistent navigation behavior
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Hide keyboard before finishing if it's visible
                hideKeyboard()
                finish()
            }
        })
    }
    
    /**
     * Launch RealWear voice dictation system
     * This will open the RealWear dictation interface for speech-to-text input
     */
    private fun launchDictation() {
        try {
            updateStatus(getString(R.string.realwear_keyboard_dictation_launching))
            
            val intent = Intent(ACTION_DICTATION)
            startActivityForResult(intent, DICTATION_REQUEST_CODE)
            
        } catch (ex: Exception) {
            // Handle cases where RealWear dictation is not available
            android.util.Log.e("KeyboardDictationActivity", "Failed to launch dictation", ex)
            updateStatus(getString(R.string.realwear_keyboard_dictation_not_available))
        }
    }
    
    /**
     * Launch software keyboard for manual text input
     * This will show the Android soft keyboard for traditional text entry
     */
    private fun launchKeyboard() {
        try {
            // Make the text field focusable and request focus
            mTextField.isFocusable = true
            mTextField.isFocusableInTouchMode = true
            mTextField.requestFocus()
            
            // Show the software keyboard
            val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.showSoftInput(mTextField, InputMethodManager.SHOW_FORCED)
            
            updateStatus(getString(R.string.realwear_keyboard_dictation_keyboard_shown))
            
        } catch (ex: Exception) {
            android.util.Log.e("KeyboardDictationActivity", "Failed to show keyboard", ex)
            updateStatus(getString(R.string.realwear_keyboard_dictation_keyboard_error))
        }
    }
    
    /**
     * Hide the software keyboard
     */
    private fun hideKeyboard() {
        try {
            val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputMethodManager.hideSoftInputFromWindow(mTextField.windowToken, 0)
            
            // Remove focus from text field
            mTextField.clearFocus()
            
            updateStatus(getString(R.string.realwear_keyboard_dictation_keyboard_hidden))
            
        } catch (ex: Exception) {
            android.util.Log.e("KeyboardDictationActivity", "Failed to hide keyboard", ex)
        }
    }
    
    /**
     * Clear all text from the input field
     */
    private fun clearTextField() {
        mTextField.setText("")
        updateStatus(getString(R.string.realwear_keyboard_dictation_text_cleared))
    }
    
    /**
     * Update the status message display
     * 
     * @param status The status message to display
     */
    private fun updateStatus(status: String) {
        txtStatus.text = "${getString(R.string.realwear_keyboard_dictation_status_label)} $status"
    }
    
    /**
     * Handle results from external activities. Receives text data from dictation.
     * 
     * @param requestCode Identifies which activity returned the result
     * @param resultCode Result status from the dictation system
     * @param data Intent containing the dictated text data
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == DICTATION_REQUEST_CODE) {
            when (resultCode) {
                Activity.RESULT_OK -> {
                    // Successfully received dictated text
                    val result = data?.getStringExtra(EXTRA_RESULT) ?: getString(R.string.realwear_keyboard_dictation_no_text)
                    
                    // Append dictated text to existing content or replace it
                    val currentText = mTextField.text.toString()
                    val newText = if (currentText.isEmpty()) {
                        result
                    } else {
                        "$currentText $result"
                    }
                    
                    mTextField.setText(newText)
                    updateStatus(getString(R.string.realwear_keyboard_dictation_success, result.length))
                }
                
                Activity.RESULT_CANCELED -> {
                    // User cancelled dictation
                    updateStatus(getString(R.string.realwear_keyboard_dictation_cancelled))
                }
                
                else -> {
                    // Unknown result code
                    updateStatus(getString(R.string.realwear_keyboard_dictation_failed))
                    android.util.Log.w("KeyboardDictationActivity", "Unknown dictation result code: $resultCode")
                }
            }
        }
    }
    
    /**
     * Called when activity is resumed
     * Update status to reflect current state
     */
    override fun onResume() {
        super.onResume()
        updateStatus(getString(R.string.realwear_keyboard_dictation_ready))
    }
}


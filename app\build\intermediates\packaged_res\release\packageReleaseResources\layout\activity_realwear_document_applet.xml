<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Document Applet Demo Layout -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white"
    tools:context=".Activities.RealWear.DocumentAppletActivity">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_document_applet_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_document_applet_description"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="24dp"
        android:lineSpacingMultiplier="1.2" />

    <!-- Document Type Sections -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- PDF Documents Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/rounded_background_light"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_pdf_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/ic_close"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_pdf_description"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    android:layout_marginBottom="12dp"
                    android:lineSpacingMultiplier="1.2" />

                <Button
                    android:id="@+id/btnLaunchPdfDocument"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:text="@string/realwear_document_open_pdf"
                    android:textSize="16sp"
                    android:background="@drawable/input_field_background"
                    android:textColor="@color/black"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Image Documents Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/rounded_background_light"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_image_section"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/ic_close"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_image_description"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    android:layout_marginBottom="12dp"
                    android:lineSpacingMultiplier="1.2" />

                <Button
                    android:id="@+id/btnLaunchImageDocument"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:text="@string/realwear_document_open_image"
                    android:textSize="16sp"
                    android:background="@drawable/input_field_background"
                    android:textColor="@color/black"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Instructions Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/rounded_background_light"
                android:padding="16dp"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_instructions_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/realwear_document_instructions"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    android:lineSpacingMultiplier="1.3" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Back Button -->
    <Button
        android:id="@+id/btnBack"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="@string/btnBack"
        android:textSize="16sp"
        android:background="@drawable/dialog_background"
        android:textColor="@color/black"
        android:drawableStart="@drawable/ic_back"
        android:drawablePadding="8dp"
        android:gravity="center"
        android:layout_marginTop="16dp" />

</LinearLayout>

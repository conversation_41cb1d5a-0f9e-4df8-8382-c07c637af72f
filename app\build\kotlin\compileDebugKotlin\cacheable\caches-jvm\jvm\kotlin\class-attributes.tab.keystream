Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivityQcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivityTcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapterecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolderPcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDtoMcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.UserNavigationJcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialogCcom.example.standardtemplate.Activities.Login_Setting.LoginActivityKcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter\com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolderFcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialogQcom.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListenerKcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivityHcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivityEcom.example.standardtemplate.Activities.RealWear.ActionButtonActivityOcom.example.standardtemplate.Activities.RealWear.ActionButtonActivity.CompanionEcom.example.standardtemplate.Activities.RealWear.AudioCaptureActivityOcom.example.standardtemplate.Activities.RealWear.AudioCaptureActivity.CompanionCcom.example.standardtemplate.Activities.RealWear.BNFGrammarActivityMcom.example.standardtemplate.Activities.RealWear.BNFGrammarActivity.CompanionFcom.example.standardtemplate.Activities.RealWear.BarcodeAppletActivityPcom.example.standardtemplate.Activities.RealWear.BarcodeAppletActivity.CompanionEcom.example.standardtemplate.Activities.RealWear.CameraAppletActivityOcom.example.standardtemplate.Activities.RealWear.CameraAppletActivity.CompanionGcom.example.standardtemplate.Activities.RealWear.DocumentAppletActivityQcom.example.standardtemplate.Activities.RealWear.DocumentAppletActivity.CompanionAcom.example.standardtemplate.Activities.RealWear.HelpMenuActivityKcom.example.standardtemplate.Activities.RealWear.HelpMenuActivity.CompanionJcom.example.standardtemplate.Activities.RealWear.KeyboardDictationActivityTcom.example.standardtemplate.Activities.RealWear.KeyboardDictationActivity.CompanionJcom.example.standardtemplate.Activities.RealWear.MicrophoneReleaseActivityTcom.example.standardtemplate.Activities.RealWear.MicrophoneReleaseActivity.CompanionDcom.example.standardtemplate.Activities.RealWear.MovieAppletActivityNcom.example.standardtemplate.Activities.RealWear.MovieAppletActivity.CompanionEcom.example.standardtemplate.Activities.RealWear.RealWearMainActivityIcom.example.standardtemplate.Activities.RealWear.SpeechRecognizerActivityScom.example.standardtemplate.Activities.RealWear.SpeechRecognizerActivity.CompanionEcom.example.standardtemplate.Activities.RealWear.TextToSpeechActivityOcom.example.standardtemplate.Activities.RealWear.TextToSpeechActivity.CompanionAcom.example.standardtemplate.Activities.Register.RegisterActivity7com.example.standardtemplate.Dialogs.ConfirmationDialog2com.example.standardtemplate.Dialogs.MessageDialog0com.example.standardtemplate.Libraries.ApiClient3com.example.standardtemplate.Libraries.ApiInterface?com.example.standardtemplate.Libraries.FirebaseMessagingServiceCcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivityMcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.CompanionGcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate?com.example.standardtemplate.Libraries.Managers.UserInfoManager9com.example.standardtemplate.Libraries.NotificationHelper:com.example.standardtemplate.Libraries.NotificationService=com.example.standardtemplate.Libraries.RealWear.DocumentUtilsBcom.example.standardtemplate.Libraries.StandardFunction.SharedPref7com.example.standardtemplate.Libraries.TicketMonitoringAcom.example.standardtemplate.Libraries.TicketMonitoring.Companion5com.example.standardtemplate.Models.AcceptedTicketDto2com.example.standardtemplate.Models.UserNavigation3com.example.standardtemplate.Models.FcmTokenRequest-com.example.standardtemplate.Models.LoginInfo1com.example.standardtemplate.Models.LoginResponse1com.example.standardtemplate.Models.NewTicketInfo9com.example.standardtemplate.Models.NewTicketListResponse0com.example.standardtemplate.Models.RegisterInfo4com.example.standardtemplate.Models.RegisterResponse.com.example.standardtemplate.Models.TicketDone/com.example.standardtemplate.Models.UserDetails                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
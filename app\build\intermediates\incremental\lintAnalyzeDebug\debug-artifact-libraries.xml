<libraries>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\838052c77a74c243fbc236b2a83bb29c\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\838052c77a74c243fbc236b2a83bb29c\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88fe8a244b19b211d69544debf26b711\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88fe8a244b19b211d69544debf26b711\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ff4ec66daf8ab6ce732fa9c600777\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ff4ec66daf8ab6ce732fa9c600777\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0ccbe3f726404611b02120431db826\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0ccbe3f726404611b02120431db826\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb34b569ef3258e736253342526ac408\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb34b569ef3258e736253342526ac408\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:23.1.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:23.1.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a8c369ee497b153189581a880c26f73e\transformed\firebase-messaging-23.1.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a73e0497c2aa14581b688e0424d5422\transformed\firebase-analytics-21.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a73e0497c2aa14581b688e0424d5422\transformed\firebase-analytics-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cf4f689c9c71cde8e3f11cdb0b424e1\transformed\play-services-measurement-api-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.1.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f26509dc17817900c686e2a9bc481a59\transformed\firebase-installations-17.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\209770c6a6d312abae6884a69d1a6e7c\transformed\firebase-datatransport-18.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e23249fb189645c69c9e7dc9bdb43009\transformed\firebase-common-20.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0b8cd4252ac4fd17a648b37896092c6d\transformed\firebase-installations-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0b8cd4252ac4fd17a648b37896092c6d\transformed\firebase-installations-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\85d1553e1b933cd2e13c79366a035914\transformed\play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5445855cde9681d61c99e66482283952\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5445855cde9681d61c99e66482283952\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\16bf436b8bc4597d053988250c8ce74a\transformed\play-services-cloud-messaging-17.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\16bf436b8bc4597d053988250c8ce74a\transformed\play-services-cloud-messaging-17.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\982821c63c9f4fddf4fde209da4b64a4\transformed\play-services-tasks-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\982821c63c9f4fddf4fde209da4b64a4\transformed\play-services-tasks-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0092f5ea9d5d7282d0a79c4ddcb3b037\transformed\play-services-measurement-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\70e14de790c7fbfea12be4c531de577c\transformed\play-services-measurement-sdk-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\70e14de790c7fbfea12be4c531de577c\transformed\play-services-measurement-sdk-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\30fcbc2027cb631cee64bbc288ae1a2d\transformed\play-services-measurement-impl-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\30fcbc2027cb631cee64bbc288ae1a2d\transformed\play-services-measurement-impl-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0b9af758b42999cdcf0b170a20e36de\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e0b9af758b42999cdcf0b170a20e36de\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0c743ef18de0635dd52288cbd7cc125\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c0c743ef18de0635dd52288cbd7cc125\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4e63efbe989c76a035b320ef0eabe9\transformed\play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5a4e63efbe989c76a035b320ef0eabe9\transformed\play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9319808835fef64a74f9cd328a789805\transformed\play-services-measurement-sdk-api-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9319808835fef64a74f9cd328a789805\transformed\play-services-measurement-sdk-api-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f58cb8cabf9d61065b18390da121800\transformed\play-services-measurement-base-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f58cb8cabf9d61065b18390da121800\transformed\play-services-measurement-base-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b5fd8a1debeeacbb6bf1c31739d4dca\transformed\play-services-basement-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ff01f55946e98ba5590edbee9d55252\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4ff01f55946e98ba5590edbee9d55252\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a33f5a5b546bf04a02987ba6cda949f5\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a33f5a5b546bf04a02987ba6cda949f5\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\adaf6fa70fd7f06c6531b10716aa2088\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\adaf6fa70fd7f06c6531b10716aa2088\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb7ed0ae63d3c49fffa8ba99a43763f\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fb7ed0ae63d3c49fffa8ba99a43763f\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0503f78dff39dbf3b5ac93bdc19beb35\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0503f78dff39dbf3b5ac93bdc19beb35\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e3d759341d5d145ead45457f9df9377\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e3d759341d5d145ead45457f9df9377\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7d6b3a42b5537fcce4e028110689293\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7d6b3a42b5537fcce4e028110689293\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c753d50888dde69c014847bbb8e727ef\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c753d50888dde69c014847bbb8e727ef\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3526b62cc93bc33321f5898ab0272f0\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b3526b62cc93bc33321f5898ab0272f0\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\751cfd8784aa86731527c4bd0f475b8d\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\751cfd8784aa86731527c4bd0f475b8d\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2b14c9dacd58ef6caf389178e5364150\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2b14c9dacd58ef6caf389178e5364150\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\312957bdb84d418f1ec872381b5fce0b\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\312957bdb84d418f1ec872381b5fce0b\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\24c2e1e083198ffee6f5c9a03744c49e\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\24c2e1e083198ffee6f5c9a03744c49e\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0\jars\classes.jar"
      resolved="androidx.core:core:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6da3c2cc95131e6e0714e67e2bba3b1b\transformed\core-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d79c492308a15d402cc6e0df535f03ea\transformed\work-runtime-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d79c492308a15d402cc6e0df535f03ea\transformed\work-runtime-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\399b06c4ccad822db27ea8d997d31162\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a2214d123087fdd9a9ec68b3df82047\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a2214d123087fdd9a9ec68b3df82047\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10f11d1ba46eb5863202a4dba7257a70\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10f11d1ba46eb5863202a4dba7257a70\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dd143acdeca3562f716e92451c15e0e\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dd143acdeca3562f716e92451c15e0e\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dedefce7f97ca08935579a0d0ad5bb96\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dedefce7f97ca08935579a0d0ad5bb96\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab93327de0b24d77747d081dda63b8d\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab93327de0b24d77747d081dda63b8d\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8dd94f69513de21c20b06bf0e3430c7\transformed\core-ktx-1.15.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8dd94f69513de21c20b06bf0e3430c7\transformed\core-ktx-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ba89cba7417c6725234382ffa717299\transformed\constraintlayout-2.2.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1ba89cba7417c6725234382ffa717299\transformed\constraintlayout-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.11.0\9d1fe9d1662de0548e08e293041140a8e4026f81\converter-gson-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.14@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\5.0.0-alpha.14\3ad5d40a9fd22189173cacf23ef665b14faa7fe8\logging-interceptor-5.0.0-alpha.14.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:5.0.0-alpha.14"/>
  <library
      name="com.squareup.okhttp3:okhttp:5.0.0-alpha.14@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\5.0.0-alpha.14\c59864766ffc0d0dd4394ec0af5e3b60bf83d10f\okhttp-5.0.0-alpha.14.jar"
      resolved="com.squareup.okhttp3:okhttp:5.0.0-alpha.14"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\93e4689b40fa05b9ac900f83fcd43ca1\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\93e4689b40fa05b9ac900f83fcd43ca1\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\eb83a6a2d7e7002fe90d8f79e2328660\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\eb83a6a2d7e7002fe90d8f79e2328660\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\31828beeb6b7b51a80534e87d678d0d6\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\31828beeb6b7b51a80534e87d678d0d6\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dde1cd2aecf197c69150140f88a0575\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4dde1cd2aecf197c69150140f88a0575\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\22e22a672941fce55110ffe0b8066895\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\22e22a672941fce55110ffe0b8066895\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f572e8528846a4c59628f97aca0a41e9\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f572e8528846a4c59628f97aca0a41e9\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e72572d97406c109820aba042079335\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1e72572d97406c109820aba042079335\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5c2983ae9cb46aab1df4ba973ed9be1d\transformed\transport-backend-cct-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c311e751f6b2aa90d5ba6348eca79b69\transformed\transport-runtime-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2c9d49b6b947efd886c9d26153d025ce\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2c9d49b6b947efd886c9d26153d025ce\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.firebase:firebase-components:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f73ad010ea82909f62961726d0f123d\transformed\firebase-components-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f73ad010ea82909f62961726d0f123d\transformed\firebase-components-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:3.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0c616e236d29c45c36dd2b291d23903b\transformed\transport-api-3.0.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0c616e236d29c45c36dd2b291d23903b\transformed\transport-api-3.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d228771ea6241ed59723249bfec0d9be\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d228771ea6241ed59723249bfec0d9be\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c22648f24485e2d43d8883fc259803\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d6c22648f24485e2d43d8883fc259803\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\33d88823fc327e0b026696428b6193df\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\33d88823fc327e0b026696428b6193df\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b9c953260bf7691a58cdb21b1d33859e\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b9c953260bf7691a58cdb21b1d33859e\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\02e051ba84bdb85e317e76b58195660b\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\02e051ba84bdb85e317e76b58195660b\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="com.squareup.okio:okio-jvm:3.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.9.0\eaa4f7858a1e80908b1a2e861e662edd7c6cbbb5\okio-jvm-3.9.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.0\e000bd084353d84c9e888f6fb341dc1f5b79d948\kotlin-stdlib-jdk8-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.0\f320478990d05e0cfaadd74f9619fd6027adbf37\kotlin-stdlib-jdk7-1.9.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.23@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.23\dbaadea1f5e68f790d242a91a38355a83ec38747\kotlin-stdlib-1.9.23.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.23"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\16ae8f7c219696ed3a6efe1ce9c31690\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\16ae8f7c219696ed3a6efe1ce9c31690\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d40dc1faef1ddf4a72793256276a703d\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d40dc1faef1ddf4a72793256276a703d\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1f3668633c8b84c1432fafa61fa109d\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a1f3668633c8b84c1432fafa61fa109d\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0907741920ba27261b68d6942f7607bc\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0907741920ba27261b68d6942f7607bc\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0412e0bb3bf34f5aff4cb7c8b46caa25\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3299f5536dbf487f656c953a266811c8\transformed\lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3299f5536dbf487f656c953a266811c8\transformed\lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cf70a94d1bfdeb3d5ad71ed532635ea9\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\77803addd44dc3665a5b9680dd40c047\transformed\room-ktx-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\77803addd44dc3665a5b9680dd40c047\transformed\room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0e91dc0e7adcc1c3ee408592a71a9d2b\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8663db974df057164c5ec84c64c10129\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.1\f7ab6170b99b9421bd4942846426ff820b552f7d\constraintlayout-core-1.1.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.1"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b1c4cf7b65312869f3edf087392f4cdf\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b1c4cf7b65312869f3edf087392f4cdf\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\461f677bb86156d6a2dc1a538b88e24d\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\461f677bb86156d6a2dc1a538b88e24d\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>

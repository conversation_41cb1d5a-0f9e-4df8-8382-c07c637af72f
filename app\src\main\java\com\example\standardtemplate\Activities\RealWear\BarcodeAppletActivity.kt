package com.example.standardtemplate.Activities.RealWear

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R

/**
 * RealWear Barcode Applet Activity
 * 
 * This activity demonstrates how to scan barcodes using a RealWear HMT device.
 * It shows how to launch the built-in barcode scanner and process the scanned results.
 * The barcode scanner supports multiple symbologies including QR codes, DataMatrix,
 * EAN/UPC, and Code 128.
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * Features:
 * - Launch RealWear's built-in barcode scanner
 * - Configure enabled barcode symbologies
 * - Process and display scanned barcode results
 * - Support for QR codes, DataMatrix, EAN/UPC, Code 128
 * - Real-time barcode result display
 */
class BarcodeAppletActivity : BaseActivity() {
    
    companion object {
        // Request code identifying the barcode scanner events
        private const val BARCODE_REQUEST_CODE = 1984
        
        // RealWear barcode scanner intent action
        private const val SCAN_BARCODE = "com.realwear.barcodereader.intent.action.SCAN_BARCODE"
        
        // Identifier for the result string returned by the barcode scanner
        private const val EXTRA_RESULT = "com.realwear.barcodereader.intent.extra.RESULT"
        
        // Available barcode symbologies - can be enabled/disabled individually
        private const val EXTRA_CODE_128 = "com.realwear.barcodereader.intent.extra.CODE_128"
        private const val EXTRA_CODE_DM = "com.realwear.barcodereader.intent.extra.CODE_DM"
        private const val EXTRA_CODE_EAN_UPC = "com.realwear.barcodereader.intent.extra.CODE_EAN_UPC"
        private const val EXTRA_CODE_QR = "com.realwear.barcodereader.intent.extra.CODE_QR"
    }
    
    // UI Components
    private lateinit var btnScanBarcode: Button
    private lateinit var btnScanQROnly: Button
    private lateinit var btnScanAllFormats: Button
    private lateinit var btnClearResults: Button
    private lateinit var btnBack: Button
    private lateinit var mBarcodeResultsView: TextView
    
    /**
     * Called when the activity is created
     * 
     * @param savedInstanceState See Android documentation
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set fullscreen mode for optimal RealWear device display
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_barcode_applet)
        
        // Initialize UI components
        initializeViews()
        
        // Setup click listeners
        setupClickListeners()
        
        // Setup back press handler
        setupBackPressedHandler()
        
        // Initialize results view
        clearBarcodeResults()
    }
    
    /**
     * Initialize all UI components
     */
    private fun initializeViews() {
        btnScanBarcode = findViewById(R.id.btnScanBarcode)
        btnScanQROnly = findViewById(R.id.btnScanQROnly)
        btnScanAllFormats = findViewById(R.id.btnScanAllFormats)
        btnClearResults = findViewById(R.id.btnClearResults)
        btnBack = findViewById(R.id.btnBack)
        mBarcodeResultsView = findViewById(R.id.barcode_textview)
    }
    
    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        // Launch barcode scanner with default settings (QR, DataMatrix, EAN/UPC enabled)
        btnScanBarcode.setOnClickListener { 
            launchBarcodeScanner(
                enableCode128 = false,
                enableDataMatrix = true,
                enableEanUpc = true,
                enableQR = true
            )
        }
        
        // Launch barcode scanner for QR codes only
        btnScanQROnly.setOnClickListener { 
            launchBarcodeScanner(
                enableCode128 = false,
                enableDataMatrix = false,
                enableEanUpc = false,
                enableQR = true
            )
        }
        
        // Launch barcode scanner with all formats enabled
        btnScanAllFormats.setOnClickListener { 
            launchBarcodeScanner(
                enableCode128 = true,
                enableDataMatrix = true,
                enableEanUpc = true,
                enableQR = true
            )
        }
        
        // Clear the barcode results display
        btnClearResults.setOnClickListener { 
            clearBarcodeResults() 
        }
        
        btnBack.setOnClickListener { finish() }
    }
    
    /**
     * Setup back press handler to maintain consistent navigation behavior
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
            }
        })
    }
    
    /**
     * Launch the RealWear barcode scanner with specified symbology configuration
     * 
     * @param enableCode128 Enable/disable Code 128 barcode format
     * @param enableDataMatrix Enable/disable DataMatrix barcode format  
     * @param enableEanUpc Enable/disable EAN/UPC barcode format
     * @param enableQR Enable/disable QR code format
     */
    private fun launchBarcodeScanner(
        enableCode128: Boolean,
        enableDataMatrix: Boolean,
        enableEanUpc: Boolean,
        enableQR: Boolean
    ) {
        // Clear previous results before scanning
        clearBarcodeResults()
        
        try {
            // Create intent for RealWear barcode scanner
            val intent = Intent(SCAN_BARCODE).apply {
                // Configure which symbologies are enabled
                // If none is specified, all are enabled by default
                putExtra(EXTRA_CODE_128, enableCode128)
                putExtra(EXTRA_CODE_DM, enableDataMatrix)
                putExtra(EXTRA_CODE_EAN_UPC, enableEanUpc)
                putExtra(EXTRA_CODE_QR, enableQR)
            }
            
            // Launch the barcode scanner
            startActivityForResult(intent, BARCODE_REQUEST_CODE)
            
        } catch (ex: Exception) {
            // Handle cases where RealWear barcode scanner is not available
            android.util.Log.e("BarcodeAppletActivity", "Failed to launch barcode scanner", ex)
            mBarcodeResultsView.text = getString(R.string.realwear_barcode_scanner_not_available)
        }
    }
    
    /**
     * Handle results from external activities. Receives barcode data from scanner.
     * 
     * @param requestCode Identifies which activity returned the result
     * @param resultCode Result status from the barcode scanner
     * @param data Intent containing the scanned barcode data
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == BARCODE_REQUEST_CODE) {
            when (resultCode) {
                Activity.RESULT_OK -> {
                    // Successfully scanned a barcode
                    val result = data?.getStringExtra(EXTRA_RESULT) ?: getString(R.string.realwear_barcode_no_data)
                    displayBarcodeResult(result)
                }
                
                Activity.RESULT_CANCELED -> {
                    // User cancelled the barcode scanning
                    mBarcodeResultsView.text = getString(R.string.realwear_barcode_scan_cancelled)
                }
                
                else -> {
                    // Unknown result code
                    mBarcodeResultsView.text = getString(R.string.realwear_barcode_scan_failed)
                    android.util.Log.w("BarcodeAppletActivity", "Unknown result code: $resultCode")
                }
            }
        }
    }
    
    /**
     * Display the scanned barcode result with formatting
     * 
     * @param result The barcode data string to display
     */
    private fun displayBarcodeResult(result: String) {
        val formattedResult = """
            ${getString(R.string.realwear_barcode_scan_success)}
            
            ${getString(R.string.realwear_barcode_result_label)}
            $result
            
            ${getString(R.string.realwear_barcode_result_length)}
            ${result.length} ${getString(R.string.realwear_barcode_characters)}
            
            ${getString(R.string.realwear_barcode_scan_time)}
            ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}
        """.trimIndent()
        
        mBarcodeResultsView.text = formattedResult
    }
    
    /**
     * Clear the barcode results display
     */
    private fun clearBarcodeResults() {
        mBarcodeResultsView.text = getString(R.string.realwear_barcode_ready_to_scan)
    }
}


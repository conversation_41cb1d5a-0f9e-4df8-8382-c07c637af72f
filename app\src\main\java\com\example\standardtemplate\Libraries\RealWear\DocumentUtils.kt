package com.example.standardtemplate.Libraries.RealWear

import android.content.Context
import android.os.Environment
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

/**
 * Document Utilities for RealWear Development
 * 
 * This utility class provides helper functions for document management in RealWear applications,
 * particularly for copying assets to external storage and managing document files.
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 */
object DocumentUtils {
    
    /**
     * Copy a file from the assets folder to external storage
     * 
     * This method copies a file from the application's assets directory to the device's
     * external storage, making it accessible to external document viewers and other applications.
     * 
     * @param context Application context for accessing assets and external storage
     * @param fileName Name of the file in the assets folder
     * @param folderName Target folder name in external storage (optional)
     * @return File object pointing to the copied file in external storage
     * @throws IOException If file copying fails due to I/O errors
     */
    @Throws(IOException::class)
    fun copyFromAssetsToExternal(
        context: Context, 
        fileName: String, 
        folderName: String? = null
    ): File {
        // Determine target directory in external storage
        val externalDir = if (folderName != null) {
            File(Environment.getExternalStorageDirectory(), folderName)
        } else {
            Environment.getExternalStorageDirectory()
        }
        
        // Create target directory if it doesn't exist
        if (!externalDir.exists()) {
            val created = externalDir.mkdirs()
            if (!created) {
                throw IOException("Failed to create directory: ${externalDir.absolutePath}")
            }
        }
        
        // Create target file
        val targetFile = File(externalDir, fileName)
        
        // If file already exists and is not empty, return it (avoid unnecessary copying)
        if (targetFile.exists() && targetFile.length() > 0) {
            return targetFile
        }
        
        // Copy file from assets to external storage
        var inputStream: InputStream? = null
        var outputStream: FileOutputStream? = null
        
        try {
            android.util.Log.d("DocumentUtils", "Opening asset file: $fileName")
            // Open input stream from assets
            inputStream = context.assets.open(fileName)
            android.util.Log.d("DocumentUtils", "Asset file opened successfully")

            // Create output stream to target file
            outputStream = FileOutputStream(targetFile)
            android.util.Log.d("DocumentUtils", "Output stream created for: ${targetFile.absolutePath}")

            // Copy file contents
            val buffer = ByteArray(8192) // 8KB buffer for efficient copying
            var bytesRead: Int
            var totalBytesRead = 0L

            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                totalBytesRead += bytesRead
            }

            // Ensure all data is written to disk
            outputStream.flush()
            android.util.Log.d("DocumentUtils", "File copied successfully. Total bytes: $totalBytesRead")

        } catch (e: Exception) {
            android.util.Log.e("DocumentUtils", "Exception during file copy: ${e.message}", e)
            android.util.Log.e("DocumentUtils", "Exception type: ${e.javaClass.simpleName}")
            // Clean up partially copied file on error
            if (targetFile.exists()) {
                targetFile.delete()
                android.util.Log.d("DocumentUtils", "Cleaned up partially copied file")
            }
            throw IOException("Failed to copy file $fileName from assets", e)
            
        } finally {
            // Close streams safely
            try {
                inputStream?.close()
            } catch (e: IOException) {
                android.util.Log.w("DocumentUtils", "Failed to close input stream", e)
            }
            
            try {
                outputStream?.close()
            } catch (e: IOException) {
                android.util.Log.w("DocumentUtils", "Failed to close output stream", e)
            }
        }
        
        return targetFile
    }
    
    /**
     * Check if a document file exists and is readable
     * 
     * @param file The file to check
     * @return true if file exists and is readable, false otherwise
     */
    fun isDocumentReadable(file: File?): Boolean {
        return file != null && file.exists() && file.canRead() && file.length() > 0
    }
    
    /**
     * Get MIME type for common document file extensions
     * 
     * @param fileName The file name to check
     * @return MIME type string, or "application/octet-stream" if unknown
     */
    fun getMimeType(fileName: String): String {
        val extension = fileName.substringAfterLast('.', "").lowercase()
        
        return when (extension) {
            "pdf" -> "application/pdf"
            "jpg", "jpeg" -> "image/jpeg"
            "png" -> "image/png"
            "gif" -> "image/gif"
            "bmp" -> "image/bmp"
            "doc" -> "application/msword"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "xls" -> "application/vnd.ms-excel"
            "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            "ppt" -> "application/vnd.ms-powerpoint"
            "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            "txt" -> "text/plain"
            "html", "htm" -> "text/html"
            "xml" -> "text/xml"
            "json" -> "application/json"
            else -> "application/octet-stream"
        }
    }
}

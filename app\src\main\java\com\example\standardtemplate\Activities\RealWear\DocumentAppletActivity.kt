package com.example.standardtemplate.Activities.RealWear

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Libraries.RealWear.DocumentUtils
import com.example.standardtemplate.R
import java.io.File
import java.io.IOException

/**
 * RealWear Document Applet Activity
 * 
 * This activity demonstrates how to open documents and images in the document viewer 
 * on a RealWear HMT device. It shows how to launch external document viewers with 
 * various file types including PDFs, images, and other document formats.
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * Features:
 * - PDF document viewing with specific page and zoom control
 * - Image document viewing
 * - File provider integration for secure file access
 * - Sample document management from assets
 * - External document viewer integration
 */
class DocumentAppletActivity : BaseActivity() {
    
    companion object {
        // Sample file configuration - can be customized for different document types
        private const val SAMPLE_PDF_FILE_NAME = "sample.pdf"
        private const val SAMPLE_IMAGE_FILE_NAME = "sample.jpg"
        private const val SAMPLE_FOLDER_NAME = "Documents"
        private const val PDF_MIME_TYPE = "application/pdf"
        private const val IMAGE_MIME_TYPE = "image/jpeg"
        
        // Document viewer control parameters
        private const val DEFAULT_PAGE = "1"
        private const val DEFAULT_ZOOM = "2"

        // Permission request code
        private const val STORAGE_PERMISSION_REQUEST_CODE = 200
    }
    
    // UI Components
    private lateinit var btnLaunchPdfDocument: Button
    private lateinit var btnLaunchImageDocument: Button
    private lateinit var btnBack: Button
    
    // File references for sample documents
    private var mSamplePdfFile: File? = null
    private var mSampleImageFile: File? = null
    
    /**
     * Called when the activity is created
     * 
     * @param savedInstanceState See Android documentation
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set fullscreen mode for optimal RealWear device display
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_document_applet)
        
        // Initialize UI components
        initializeViews()

        // Check storage permission and setup sample documents
        checkStoragePermissionAndInitialize()
        
        // Setup click listeners
        setupClickListeners()
        
        // Setup back press handler
        setupBackPressedHandler()
    }
    
    /**
     * Check storage permission and initialize documents
     */
    private fun checkStoragePermissionAndInitialize() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {

            Log.d("DocumentAppletActivity", "Storage permission not granted, requesting permission")

            ActivityCompat.requestPermissions(
                this,
                arrayOf(android.Manifest.permission.WRITE_EXTERNAL_STORAGE),
                STORAGE_PERMISSION_REQUEST_CODE
            )
        } else {
            initializeSampleDocuments()
        }
    }

    /**
     * Handle permission request results
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            STORAGE_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d("DocumentAppletActivity", "Storage permission granted")
                    initializeSampleDocuments()
                } else {
                    Log.w("DocumentAppletActivity", "Storage permission denied")
                    Toast.makeText(this, "需要存储权限才能访问文档", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Initialize all UI components
     */
    private fun initializeViews() {
        btnLaunchPdfDocument = findViewById(R.id.btnLaunchPdfDocument)
        btnLaunchImageDocument = findViewById(R.id.btnLaunchImageDocument)
        btnBack = findViewById(R.id.btnBack)
    }
    
    /**
     * Initialize sample documents by copying them from assets to external storage
     * This ensures the documents are accessible by external document viewers
     */
    private fun initializeSampleDocuments() {
        try {
            Log.d("DocumentAppletActivity", "Starting to initialize sample documents...")

            // Copy sample PDF from assets to external storage
            mSamplePdfFile = DocumentUtils.copyFromAssetsToExternal(
                this,
                SAMPLE_PDF_FILE_NAME,
                SAMPLE_FOLDER_NAME
            )
            Log.d("DocumentAppletActivity", "PDF file copied: ${mSamplePdfFile?.absolutePath}")
            Log.d("DocumentAppletActivity", "PDF file exists: ${mSamplePdfFile?.exists()}")

            // Copy sample image from assets to external storage
            Log.d("DocumentAppletActivity", "Attempting to copy image file: $SAMPLE_IMAGE_FILE_NAME")
            mSampleImageFile = DocumentUtils.copyFromAssetsToExternal(
                this,
                SAMPLE_IMAGE_FILE_NAME,
                SAMPLE_FOLDER_NAME
            )

            Log.d("DocumentAppletActivity", "Sample image file: ${mSampleImageFile?.absolutePath}")
            Log.d("DocumentAppletActivity", "Sample image exists: ${mSampleImageFile?.exists()}")
            Log.d("DocumentAppletActivity", "Sample image size: ${mSampleImageFile?.length()} bytes")
            Log.d("DocumentAppletActivity", "Sample image readable: ${mSampleImageFile?.canRead()}")

        } catch (ex: IOException) {
            Log.e("DocumentAppletActivity", "IOException during file copy: ${ex.message}", ex)
            Toast.makeText(
                this,
                getString(R.string.realwear_document_copy_failed),
                Toast.LENGTH_LONG
            ).show()

            // Log the error for debugging
            android.util.Log.e("DocumentAppletActivity", "Failed to copy sample files", ex)
        } catch (ex: Exception) {
            Log.e("DocumentAppletActivity", "Unexpected exception during file copy: ${ex.message}", ex)
            Toast.makeText(
                this,
                "文件复制时发生未知错误: ${ex.message}",
                Toast.LENGTH_LONG
            ).show()
        }
    }
    
    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        btnLaunchPdfDocument.setOnClickListener { 
            launchDocument(mSamplePdfFile, PDF_MIME_TYPE, "PDF") 
        }
        
        btnLaunchImageDocument.setOnClickListener {
            launchImageDocument()
        }
        
        btnBack.setOnClickListener { finish() }

        // Add debug button to check file status (temporary for debugging)
        findViewById<Button>(R.id.btnLaunchPdfDocument).setOnLongClickListener {
            showFileDebugInfo()
            true
        }
    }
    
    /**
     * Setup back press handler to maintain consistent navigation behavior
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
            }
        })
    }
    
    /**
     * Launch document viewer for the specified file
     *
     * @param file The document file to open
     * @param mimeType The MIME type of the document
     * @param documentType Human-readable document type for error messages
     */
    private fun launchDocument(file: File?, mimeType: String, documentType: String) {
        Log.d("DocumentAppletActivity", "Attempting to launch $documentType document")
        Log.d("DocumentAppletActivity", "File path: ${file?.absolutePath}")
        Log.d("DocumentAppletActivity", "File exists: ${file?.exists()}")
        Log.d("DocumentAppletActivity", "File size: ${file?.length()} bytes")
        Log.d("DocumentAppletActivity", "File readable: ${file?.canRead()}")
        Log.d("DocumentAppletActivity", "MIME type: $mimeType")

        // Validate that the file exists
        if (file == null) {
            Log.e("DocumentAppletActivity", "File is null for $documentType")
            Toast.makeText(
                applicationContext,
                "文件对象为空 - $documentType",
                Toast.LENGTH_LONG
            ).show()
            return
        }

        if (!file.exists()) {
            Log.e("DocumentAppletActivity", "File does not exist: ${file.absolutePath}")
            Toast.makeText(
                applicationContext,
                getString(R.string.realwear_document_file_not_found, documentType),
                Toast.LENGTH_LONG
            ).show()
            return
        }

        if (file.length() == 0L) {
            Log.e("DocumentAppletActivity", "File is empty: ${file.absolutePath}")
            Toast.makeText(
                applicationContext,
                "文件为空 - $documentType",
                Toast.LENGTH_LONG
            ).show()
            return
        }

        try {
            Log.d("DocumentAppletActivity", "Creating FileProvider URI...")
            // Create content URI using FileProvider for secure file access
            val contentUri = FileProvider.getUriForFile(
                applicationContext,
                "${applicationContext.packageName}.fileprovider",
                file
            )
            Log.d("DocumentAppletActivity", "FileProvider URI created: $contentUri")

            // Check if there are apps that can handle this intent
            val packageManager = applicationContext.packageManager
            val viewIntent = Intent(Intent.ACTION_VIEW).apply {
                addCategory(Intent.CATEGORY_DEFAULT)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                setDataAndType(contentUri, mimeType)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

                // Add document viewer control parameters (for PDF documents)
                if (mimeType == PDF_MIME_TYPE) {
                    putExtra("page", DEFAULT_PAGE) // Open at specific page
                    putExtra("zoom", DEFAULT_ZOOM) // Open at specific zoom level
                    Log.d("DocumentAppletActivity", "Added PDF-specific parameters")
                }
            }

            // Check if there are apps that can handle this intent
            val resolveInfos = packageManager.queryIntentActivities(viewIntent, PackageManager.MATCH_DEFAULT_ONLY)
            Log.d("DocumentAppletActivity", "Found ${resolveInfos.size} apps that can handle $mimeType")

            if (resolveInfos.isEmpty()) {
                Log.w("DocumentAppletActivity", "No apps found to handle $mimeType")
                Toast.makeText(
                    applicationContext,
                    "设备上没有可以打开${documentType}文件的应用",
                    Toast.LENGTH_LONG
                ).show()
                return
            }

            // Try with specific MIME type first, then fallback to generic image type
            if (mimeType == IMAGE_MIME_TYPE && documentType == "Image") {
                // Try with specific JPEG MIME type first
                val specificMimeType = "image/jpeg"
                val specificIntent = Intent(Intent.ACTION_VIEW).apply {
                    addCategory(Intent.CATEGORY_DEFAULT)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    setDataAndType(contentUri, specificMimeType)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                val specificResolveInfos = packageManager.queryIntentActivities(specificIntent, PackageManager.MATCH_DEFAULT_ONLY)
                Log.d("DocumentAppletActivity", "Found ${specificResolveInfos.size} apps that can handle $specificMimeType")

                if (specificResolveInfos.isNotEmpty()) {
                    Log.d("DocumentAppletActivity", "Using specific MIME type: $specificMimeType")
                    startActivity(specificIntent)
                    return
                }
            }

            Log.d("DocumentAppletActivity", "Intent created, launching document viewer...")
            // Launch the document viewer
            startActivity(viewIntent)
            Log.d("DocumentAppletActivity", "Document viewer launched successfully")

        } catch (ex: Exception) {
            // Handle cases where no suitable document viewer is available
            Log.e("DocumentAppletActivity", "Failed to launch document viewer for $documentType", ex)
            Log.e("DocumentAppletActivity", "Exception type: ${ex.javaClass.simpleName}")
            Log.e("DocumentAppletActivity", "Exception message: ${ex.message}")

            Toast.makeText(
                applicationContext,
                getString(R.string.realwear_document_viewer_not_available, documentType) + "\n错误: ${ex.message}",
                Toast.LENGTH_LONG
            ).show()

            android.util.Log.e("DocumentAppletActivity", "Failed to launch document viewer", ex)
        }
    }

    /**
     * Launch image document with multiple fallback options
     */
    private fun launchImageDocument() {
        Log.d("DocumentAppletActivity", "=== Starting image document launch ===")

        // First, show debug info
        showFileDebugInfo()

        if (mSampleImageFile == null || !mSampleImageFile!!.exists()) {
            Log.e("DocumentAppletActivity", "Image file is null or doesn't exist")
            Toast.makeText(
                applicationContext,
                "图片文件不存在，请检查assets文件夹中的sample.jpg",
                Toast.LENGTH_LONG
            ).show()
            return
        }

        val file = mSampleImageFile!!
        Log.d("DocumentAppletActivity", "=== 图片文件信息 ===")
        Log.d("DocumentAppletActivity", "文件路径: ${file.absolutePath}")
        Log.d("DocumentAppletActivity", "文件大小: ${file.length()} bytes")
        Log.d("DocumentAppletActivity", "文件存在: ${file.exists()}")
        Log.d("DocumentAppletActivity", "文件可读: ${file.canRead()}")

        // Try the same approach as PDF first
        Log.d("DocumentAppletActivity", "=== 尝试与PDF相同的方法 ===")
        launchDocument(file, IMAGE_MIME_TYPE, "图片")
    }

    /**
     * Debug method to show file information
     */
    private fun showFileDebugInfo() {
        val debugInfo = StringBuilder()
        debugInfo.append("=== 文件调试信息 ===\n\n")

        // PDF文件信息
        debugInfo.append("📄 PDF文件:\n")
        if (mSamplePdfFile != null) {
            debugInfo.append("路径: ${mSamplePdfFile!!.absolutePath}\n")
            debugInfo.append("存在: ${mSamplePdfFile!!.exists()}\n")
            debugInfo.append("大小: ${mSamplePdfFile!!.length()} bytes\n")
            debugInfo.append("可读: ${mSamplePdfFile!!.canRead()}\n")
        } else {
            debugInfo.append("PDF文件对象为null\n")
        }

        debugInfo.append("\n🖼️ 图片文件:\n")
        if (mSampleImageFile != null) {
            debugInfo.append("路径: ${mSampleImageFile!!.absolutePath}\n")
            debugInfo.append("存在: ${mSampleImageFile!!.exists()}\n")
            debugInfo.append("大小: ${mSampleImageFile!!.length()} bytes\n")
            debugInfo.append("可读: ${mSampleImageFile!!.canRead()}\n")
        } else {
            debugInfo.append("图片文件对象为null\n")
        }

        // 外部存储信息
        debugInfo.append("\n📁 外部存储:\n")
        debugInfo.append("外部存储路径: ${Environment.getExternalStorageDirectory().absolutePath}\n")
        debugInfo.append("外部存储可用: ${Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED}\n")

        // 应用信息
        debugInfo.append("\n📱 应用信息:\n")
        debugInfo.append("包名: ${applicationContext.packageName}\n")
        debugInfo.append("FileProvider Authority: ${applicationContext.packageName}.fileprovider\n")

        Log.d("DocumentAppletActivity", debugInfo.toString())

        Toast.makeText(
            this,
            debugInfo.toString(),
            Toast.LENGTH_LONG
        ).show()
    }
}

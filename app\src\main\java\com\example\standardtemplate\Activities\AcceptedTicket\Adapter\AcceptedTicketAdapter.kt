package com.example.standardtemplate.Activities.AcceptedTicket.Adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.R

class AcceptedTicketAdapter(private val context: Context, val acceptedTicketList: List<AcceptedTicketDto>, //get sharedPreference
                            val username: String
) :
    RecyclerView.Adapter<AcceptedTicketAdapter.TicketViewHolder>() {
        private lateinit var sharedPref: SharedPref

    // Define ViewHolder properly
    class TicketViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val txtMachineId: TextView = itemView.findViewById(R.id.txtMachineId)
        val txtErrorMsg: TextView = itemView.findViewById(R.id.txtErrorMsg)
        val txtStatus: TextView = itemView.findViewById(R.id.txtMachineStatus)
        val txtAttendBy: TextView = itemView.findViewById(R.id.txtAcceptedby)
        val cardView: CardView = itemView.findViewById(R.id.cardView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_accepted_ticket_list, parent, false)
        return TicketViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: TicketViewHolder, position: Int) {
        val ticket = acceptedTicketList[position]

        // Ensure data is properly displayed
        holder.txtErrorMsg.text = "Error: \n${ticket.errorMsg ?: "N/A"}"
        holder.txtMachineId.text = "Machine: \n${ticket.machineId ?: "Unknown"}"
        holder.txtStatus.text = "Machine Status: \n${ticket.machineStatus ?: "Pending"}"
        holder.txtAttendBy.text = "Attended By: \n${username ?: "Not Assigned"}"

        // Handle CardView Click Event
        holder.cardView.setOnClickListener {
            val context = holder.itemView.context
            val intent = Intent(context,  AcceptedTicketDetailsActivity::class.java).apply {
                putExtra("id", ticket.id)
                putExtra("machineId", ticket.machineId)
                putExtra("errorMsg", ticket.errorMsg)
                putExtra("machineStatus", ticket.machineStatus)
                putExtra("createdAt", ticket.createdAt)
                putExtra("attendby", ticket.attendBy)
                putExtra("ackAt", ticket.ackAt)
                putExtra("status", ticket.status)
                putExtra("remedy", ticket.remedy)
                putExtra("completeAt", ticket.completeAt)
                putExtra("downtime", ticket.machineDowntimeStr)
            }
            context.startActivity(intent)
        }
    }

    override fun getItemCount(): Int = acceptedTicketList.size
}

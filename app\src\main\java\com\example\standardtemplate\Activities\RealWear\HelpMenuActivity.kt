/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity that shows how to add commands to the Show Help dialog on a RealWear device.
 * This demo demonstrates how to register custom voice commands in the RealWear help system.
 */
class HelpMenuActivity : BaseActivity() {

    companion object {
        private const val TAG = "HelpMenuActivity"
        
        // Update help commands intent action
        private const val ACTION_UPDATE_HELP = "com.realwear.wearhf.intent.action.UPDATE_HELP_COMMANDS"
        
        // Identifiers for adding commands to the Show Help screen
        private const val EXTRA_TEXT = "com.realwear.wearhf.intent.extra.HELP_COMMANDS"
        private const val EXTRA_SOURCE = "com.realwear.wearhf.intent.extra.SOURCE_PACKAGE"
    }

    private lateinit var mStatusView: TextView
    private lateinit var mCommandsView: TextView
    private lateinit var btnAddCommands: Button
    private lateinit var btnClearCommands: Button
    private lateinit var btnRefreshCommands: Button
    private lateinit var btnBack: Button
    
    private var commandUpdateCount = 0
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    private val currentCommands = ArrayList<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_help_menu)

        initializeViews()
        setupDefaultCommands()
        updateStatus(getString(R.string.realwear_help_ready))
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mStatusView = findViewById(R.id.statusView)
        mCommandsView = findViewById(R.id.commandsView)
        btnAddCommands = findViewById(R.id.btnAddCommands)
        btnClearCommands = findViewById(R.id.btnClearCommands)
        btnRefreshCommands = findViewById(R.id.btnRefreshCommands)
        btnBack = findViewById(R.id.btnBack)

        // Setup click listeners
        btnAddCommands.setOnClickListener {
            addSampleCommands()
        }
        
        btnClearCommands.setOnClickListener {
            clearHelpCommands()
        }
        
        btnRefreshCommands.setOnClickListener {
            refreshCommands()
        }
        
        btnBack.setOnClickListener {
            finish()
        }
    }

    /**
     * Setup default help commands
     */
    private fun setupDefaultCommands() {
        currentCommands.clear()
        currentCommands.addAll(listOf(
            getString(R.string.realwear_help_command_navigate),
            getString(R.string.realwear_help_command_select),
            getString(R.string.realwear_help_command_back),
            getString(R.string.realwear_help_command_home)
        ))
        updateCommandsDisplay()
    }

    override fun onResume() {
        super.onResume()
        
        // Automatically send default commands when activity resumes
        sendHelpCommands(currentCommands)
        updateStatus(getString(R.string.realwear_help_commands_active))
    }

    /**
     * Add sample voice commands to the help menu
     */
    private fun addSampleCommands() {
        try {
            Log.d(TAG, "Adding sample commands to help menu")
            
            // Add some new sample commands
            val newCommands = listOf(
                getString(R.string.realwear_help_command_record),
                getString(R.string.realwear_help_command_playback),
                getString(R.string.realwear_help_command_settings),
                getString(R.string.realwear_help_command_help)
            )
            
            currentCommands.addAll(newCommands)
            sendHelpCommands(currentCommands)
            updateCommandsDisplay()
            
            updateStatus(getString(R.string.realwear_help_commands_added, newCommands.size))
            Toast.makeText(this, getString(R.string.realwear_help_commands_added, newCommands.size), Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Successfully added ${newCommands.size} commands")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to add help commands", e)
            updateStatus(getString(R.string.realwear_help_add_failed))
            Toast.makeText(this, R.string.realwear_help_add_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Clear all custom help commands
     */
    private fun clearHelpCommands() {
        try {
            Log.d(TAG, "Clearing help commands")
            
            // Send empty list to clear commands
            sendHelpCommands(emptyList())
            currentCommands.clear()
            updateCommandsDisplay()
            
            updateStatus(getString(R.string.realwear_help_commands_cleared))
            Toast.makeText(this, R.string.realwear_help_commands_cleared, Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Help commands cleared successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear help commands", e)
            updateStatus(getString(R.string.realwear_help_clear_failed))
            Toast.makeText(this, R.string.realwear_help_clear_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Refresh help commands with current list
     */
    private fun refreshCommands() {
        try {
            Log.d(TAG, "Refreshing help commands")
            
            sendHelpCommands(currentCommands)
            updateStatus(getString(R.string.realwear_help_commands_refreshed, currentCommands.size))
            Toast.makeText(this, getString(R.string.realwear_help_commands_refreshed, currentCommands.size), Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Help commands refreshed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh help commands", e)
            updateStatus(getString(R.string.realwear_help_refresh_failed))
            Toast.makeText(this, R.string.realwear_help_refresh_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Send help commands to RealWear system
     */
    private fun sendHelpCommands(commands: List<String>) {
        try {
            commandUpdateCount++
            
            val intent = Intent(ACTION_UPDATE_HELP).apply {
                putStringArrayListExtra(EXTRA_TEXT, ArrayList(commands))
                putExtra(EXTRA_SOURCE, packageName)
            }
            
            sendBroadcast(intent)
            
            val timestamp = dateFormatter.format(Date())
            Log.d(TAG, "[$timestamp] Help commands broadcast sent - Update #$commandUpdateCount, Commands: ${commands.size}")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send help commands broadcast", e)
            throw e
        }
    }

    /**
     * Update the commands display
     */
    private fun updateCommandsDisplay() {
        if (currentCommands.isEmpty()) {
            mCommandsView.text = getString(R.string.realwear_help_no_commands)
        } else {
            val commandsText = currentCommands.mapIndexed { index, command ->
                "${index + 1}. \"$command\""
            }.joinToString("\n")
            
            mCommandsView.text = getString(R.string.realwear_help_commands_list, commandsText)
        }
    }

    /**
     * Update the status display
     */
    private fun updateStatus(status: String) {
        mStatusView.text = getString(R.string.realwear_help_status_label, status)
    }

    override fun onPause() {
        super.onPause()
        
        // Clear help commands when leaving the activity to avoid conflicts
        try {
            sendHelpCommands(emptyList())
            Log.d(TAG, "Help commands cleared on pause")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear help commands on pause", e)
        }
    }
}

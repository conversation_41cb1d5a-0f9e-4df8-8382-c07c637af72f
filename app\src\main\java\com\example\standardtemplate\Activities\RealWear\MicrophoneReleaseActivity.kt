/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity that shows how to release the microphones on a RealWear device.
 * This demo allows control over microphone access and voice recognition.
 */
class MicrophoneReleaseActivity : BaseActivity() {

    companion object {
        private const val TAG = "MicrophoneReleaseActivity"
        
        // Intent actions to request and release the microphone
        private const val ACTION_RELEASE_MIC = "com.realwear.wearhf.intent.action.RELEASE_MIC"
        private const val ACTION_MIC_RELEASED = "com.realwear.wearhf.intent.action.MIC_RELEASED"
        
        // Identifiers for the settings which allow modification of the on screen message
        private const val EXTRA_MUTE_TEXT = "com.realwear.wearhf.intent.extra.MUTE_TEXT"
        private const val EXTRA_HIDE_TEXT = "com.realwear.wearhf.intent.extra.HIDE_TEXT"
        private const val EXTRA_SOURCE_PACKAGE = "com.realwear.wearhf.intent.extra.SOURCE_PACKAGE"
    }

    private lateinit var mStatusView: TextView
    private lateinit var mHistoryView: TextView
    private lateinit var btnReleaseMicrophone: Button
    private lateinit var btnRestoreMicrophone: Button
    private lateinit var btnClearHistory: Button
    private lateinit var btnBack: Button
    private lateinit var switchHideText: Switch
    private lateinit var switchCustomMessage: Switch
    
    private var eventCount = 0
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    private var isMicrophoneReleased = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_microphone_release)

        initializeViews()
        updateStatus(getString(R.string.realwear_microphone_ready))
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mStatusView = findViewById(R.id.statusView)
        mHistoryView = findViewById(R.id.historyView)
        btnReleaseMicrophone = findViewById(R.id.btnReleaseMicrophone)
        btnRestoreMicrophone = findViewById(R.id.btnRestoreMicrophone)
        btnClearHistory = findViewById(R.id.btnClearHistory)
        btnBack = findViewById(R.id.btnBack)
        switchHideText = findViewById(R.id.switchHideText)
        switchCustomMessage = findViewById(R.id.switchCustomMessage)

        // Set initial values
        mHistoryView.text = getString(R.string.realwear_microphone_history_empty)
        
        // Setup click listeners
        btnReleaseMicrophone.setOnClickListener {
            releaseMicrophone()
        }
        
        btnRestoreMicrophone.setOnClickListener {
            restoreMicrophone()
        }
        
        btnClearHistory.setOnClickListener {
            clearHistory()
        }
        
        btnBack.setOnClickListener {
            finish()
        }
        
        // Initially disable restore button
        updateButtonStates()
    }

    override fun onResume() {
        super.onResume()
        
        try {
            // Register to receive microphone events
            val filter = IntentFilter().apply {
                addAction(ACTION_MIC_RELEASED)
                addAction(ACTION_RELEASE_MIC)
            }
            registerReceiver(micBroadcastReceiver, filter)
            Log.d(TAG, "Microphone broadcast receiver registered")
            updateStatus(getString(R.string.realwear_microphone_listening))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register microphone receiver", e)
            Toast.makeText(this, R.string.realwear_microphone_register_failed, Toast.LENGTH_LONG).show()
        }
    }

    override fun onPause() {
        super.onPause()
        
        // Make sure we release the microphone on pause so we don't go into the background
        // with it still blocking ASR
        if (isMicrophoneReleased) {
            restoreMicrophone()
        }
        
        try {
            // Unregister broadcast receiver to prevent memory leaks
            unregisterReceiver(micBroadcastReceiver)
            Log.d(TAG, "Microphone broadcast receiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unregister microphone receiver", e)
        }
    }

    /**
     * Release the microphone to disable voice recognition
     */
    private fun releaseMicrophone() {
        try {
            Log.d(TAG, "Releasing microphone")
            
            val intent = Intent(ACTION_RELEASE_MIC).apply {
                putExtra(EXTRA_SOURCE_PACKAGE, packageName)
                
                // Configure display options based on switch states
                putExtra(EXTRA_HIDE_TEXT, switchHideText.isChecked)
                
                if (switchCustomMessage.isChecked) {
                    putExtra(EXTRA_MUTE_TEXT, getString(R.string.realwear_microphone_custom_message_text))
                }
            }
            
            sendBroadcast(intent)
            
            // Update UI
            isMicrophoneReleased = true
            updateButtonStates()
            updateStatus(getString(R.string.realwear_microphone_releasing))
            addToHistory("Release Request", "Microphone release requested")
            
            Log.d(TAG, "Microphone release broadcast sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release microphone", e)
            updateStatus(getString(R.string.realwear_microphone_release_failed))
            Toast.makeText(this, R.string.realwear_microphone_release_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Restore microphone access for voice recognition
     */
    private fun restoreMicrophone() {
        try {
            Log.d(TAG, "Restoring microphone")
            
            val intent = Intent(ACTION_MIC_RELEASED).apply {
                putExtra(EXTRA_SOURCE_PACKAGE, packageName)
            }
            
            sendBroadcast(intent)
            
            // Update UI
            isMicrophoneReleased = false
            updateButtonStates()
            updateStatus(getString(R.string.realwear_microphone_restoring))
            addToHistory("Restore Request", "Microphone restore requested")
            
            Log.d(TAG, "Microphone restore broadcast sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore microphone", e)
            updateStatus(getString(R.string.realwear_microphone_restore_failed))
            Toast.makeText(this, R.string.realwear_microphone_restore_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Update button enabled/disabled states based on microphone status
     */
    private fun updateButtonStates() {
        btnReleaseMicrophone.isEnabled = !isMicrophoneReleased
        btnRestoreMicrophone.isEnabled = isMicrophoneReleased
        
        // Update button text colors to indicate state
        btnReleaseMicrophone.alpha = if (isMicrophoneReleased) 0.5f else 1.0f
        btnRestoreMicrophone.alpha = if (isMicrophoneReleased) 1.0f else 0.5f
    }

    /**
     * Add event to history
     */
    private fun addToHistory(eventType: String, description: String) {
        eventCount++
        val timestamp = dateFormatter.format(Date())
        val currentHistory = mHistoryView.text.toString()
        val newEntry = "[$timestamp] Event #$eventCount - $eventType:\n$description\n"
        
        val updatedHistory = if (currentHistory == getString(R.string.realwear_microphone_history_empty)) {
            newEntry
        } else {
            "$currentHistory\n$newEntry"
        }
        
        mHistoryView.text = updatedHistory
    }

    /**
     * Clear all history and reset the display
     */
    private fun clearHistory() {
        eventCount = 0
        mHistoryView.text = getString(R.string.realwear_microphone_history_empty)
        updateStatus(getString(R.string.realwear_microphone_history_cleared))
        
        Toast.makeText(this, R.string.realwear_microphone_history_cleared, Toast.LENGTH_SHORT).show()
        Log.d(TAG, "Microphone event history cleared")
    }

    /**
     * Update the status display
     */
    private fun updateStatus(status: String) {
        mStatusView.text = getString(R.string.realwear_microphone_status_label, status)
    }

    /**
     * Broadcast Receiver to receive microphone release actions
     */
    private val micBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            try {
                val sourcePackage = intent?.getStringExtra(EXTRA_SOURCE_PACKAGE)
                
                // Ignore our own broadcasts to prevent loops
                if (sourcePackage == applicationContext.packageName) {
                    return
                }
                
                when (intent?.action) {
                    ACTION_MIC_RELEASED -> {
                        // The system has now released the microphones
                        Log.d(TAG, "Microphone released by system")
                        isMicrophoneReleased = true
                        updateButtonStates()
                        updateStatus(getString(R.string.realwear_microphone_released))
                        addToHistory("System Event", "Microphone released by system")
                    }
                    
                    ACTION_RELEASE_MIC -> {
                        // The system wants to access the microphone so release and inform the system
                        Log.d(TAG, "System requesting microphone access")
                        val newIntent = Intent(ACTION_MIC_RELEASED).apply {
                            putExtra(EXTRA_SOURCE_PACKAGE, applicationContext.packageName)
                        }
                        sendBroadcast(newIntent)
                        
                        isMicrophoneReleased = false
                        updateButtonStates()
                        updateStatus(getString(R.string.realwear_microphone_system_request))
                        addToHistory("System Request", "System requested microphone access - automatically released")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing microphone event", e)
                updateStatus(getString(R.string.realwear_microphone_processing_error))
            }
        }
    }
}

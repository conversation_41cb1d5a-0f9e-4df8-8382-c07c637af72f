<variant
    name="debug"
    package="com.example.standardtemplate"
    minSdkVersion="26"
    targetSdkVersion="35"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.11.1"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba7b0e7b1f0ada340dcc6b717f69f6cd\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\processDebugResources\R.jar"
      type="MAIN"
      applicationId="com.example.standardtemplate"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out"
      generatedResourceFolders="build\generated\res\processDebugGoogleServices;build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.13\transforms\ba7b0e7b1f0ada340dcc6b717f69f6cd\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- Permissions for network access -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- Foreground service permissions -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- background permission -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <!-- storage permission -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Camera permissions for RealWear functionality -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />

    <application
        android:name=".Libraries.LanguageSetting.StandardTemplate"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.StandardTemplate"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".Activities.Register.RegisterActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.AcceptedTicket.AcceptedTicketListActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.AcceptedTicket.AcceptedTicketDetailsActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.NewTickets.NewTicketDetailsActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.NewTickets.NewTicketListActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.Login_Setting.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- RealWear功能相关Activity -->
        <activity
            android:name=".Activities.RealWear.RealWearMainActivity"
            android:exported="false" />
        <activity
            android:name=".Activities.RealWear.ActionButtonActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.CameraAppletActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.DocumentAppletActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.MovieAppletActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.BarcodeAppletActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.KeyboardDictationActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".Activities.RealWear.SpeechRecognizerActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.TextToSpeechActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.MicrophoneReleaseActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.AudioCaptureActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.HelpMenuActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />
        <activity
            android:name=".Activities.RealWear.BNFGrammarActivity"
            android:exported="false"
            android:screenOrientation="landscape"
            android:configChanges="orientation|keyboardHidden|screenSize" />

        <service
            android:name=".Libraries.TicketMonitoring"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
        <service
            android:name=".Libraries.NotificationService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />
<!--        Implement Firebase messaging service-->
        <service
            android:name=".Libraries.FirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        
        <!-- FileProvider for secure file sharing (RealWear Document functionality) -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>
    </application>

</manifest>
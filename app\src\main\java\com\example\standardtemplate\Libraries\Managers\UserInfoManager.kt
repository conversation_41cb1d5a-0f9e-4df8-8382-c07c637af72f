package com.example.standardtemplate.Libraries.Managers

import com.example.standardtemplate.Models.LoginResponse
import com.example.standardtemplate.Models.UserDetails
import java.util.Date

object UserInfoManager {
    //declare all information needed to store
    var username: String? = null
    var fullname: String? = null
    var userId : Int = 0
    var token: String? = null
    var expiration: Date? = null

    /*
    used to clear all data stored in the UserInfoManager
     */
    fun clearUserData(){
        username = null
        fullname = null
        userId = 0
        token = null
        expiration = null
    }

    /**
     *  setAuthentication --> save the response from the api to the userinfoManager as the token is needed for authenticate the user
     *  @param token the token from the api, passed from the login function
     *  @param expiration the expiration(date) from the api, passed from the login function
     */
    fun setAuthentication(authentication: LoginResponse){
        this.token = authentication.token
        this.expiration = authentication.expiration
    }

    /**
     *  setUserData --> save the response from the api to the userinfoManager as the token is needed for authenticate the user
     *  @param userDetails the userDetails from the api, passed from the login function
     */
    fun setUserData(userDetails: UserDetails){
        username = userDetails.username
        fullname = userDetails.fullname
        userId = userDetails.id
    }

}
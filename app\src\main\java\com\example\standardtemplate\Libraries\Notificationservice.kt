package com.example.standardtemplate.Libraries

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.R


class NotificationService : Service() {
    private val channelId = "message_channel"

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel() // ✅ Ensure the channel exists
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 🔹 Step 1: Call startForeground() IMMEDIATELY
        startForeground(1, createForegroundNotification())

        // Step 2: Get display data
        val title = intent?.getStringExtra("title") ?: "Default Title"
        val message = intent?.getStringExtra("message") ?: "Default Message"

        // Step 3: Send actual notification
        sendNotification(title, message)

        // Step 4: Stop service after notification is sent
        stopSelf()

        return START_NOT_STICKY
    }

    override fun onBind(p0: Intent?): IBinder? = null

    /**
     *  sendNotification --> send a notification to the user
     *  @param title the title of the notification
     *  @param msg the message that want to show to the user
     */
    private fun sendNotification(title: String, message: String) {
        val notification = createNotification(title, message)
        val manager = NotificationManagerCompat.from(this)

        if (manager.areNotificationsEnabled()) {
            manager.notify(System.currentTimeMillis().toInt(), notification)
        } else {
            Log.w("NOTIFICATION", "Notifications are disabled for this app.")
        }
    }

    /**
     * create a notification before send to the user
     * @param title the title of the notification
     * @param message the message that want to show to the user
     * @return the notification, works together with sendNotification function
     */
    private fun createNotification(title: String, message: String): Notification {
        val intent = Intent(this, NewTicketListActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true) // ✅ Allow auto-dismiss when clicked
            .setContentIntent(pendingIntent)
            .build()
    }

    //used to display a notification while the service is running
    private fun createForegroundNotification(): Notification {
        return NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("Service Running")
            .setContentText("Processing notifications...")
            .setPriority(NotificationCompat.PRIORITY_LOW) // ✅ Foreground notification should be LOW priority
            .setAutoCancel(false)
            .build()
    }

    // ✅ Ensure the channel exists
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Message Notifications",
                NotificationManager.IMPORTANCE_HIGH
            )
            getSystemService(NotificationManager::class.java).createNotificationChannel(channel)
        }
    }
}

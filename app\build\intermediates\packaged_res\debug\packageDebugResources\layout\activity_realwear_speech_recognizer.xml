<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_description"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Voice Command Display Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_current_command"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/quantityView"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:text="@string/realwear_speech_recognizer_no_command_yet"
        android:textSize="20sp"
        android:textColor="@color/primary_color"
        android:textStyle="bold"
        android:background="@drawable/input_field_background"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:contentDescription="Voice command display" />

    <!-- Status Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_status_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_status_ready"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnClearResults"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_speech_recognizer_clear_results"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnBack"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_speech_recognizer_back"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Results History Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_results_title"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/input_field_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/resultsView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_speech_recognizer_ready_message"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- Instructions Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_instructions_title"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_speech_recognizer_instructions"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:lineSpacingExtra="2dp" />

</LinearLayout>

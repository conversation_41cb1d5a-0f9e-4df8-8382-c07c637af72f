[{"merged": "com.example.standardtemplate.app-release-46:/drawable_ic_back.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/ic_back.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_ic_close.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/ic_close.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_keyboard_dictation.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_keyboard_dictation.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_camera_applet.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_camera_applet.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_accepted_ticket_details.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_accepted_ticket_details.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_bnf_grammar.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_bnf_grammar.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_ic_noti_background.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/ic_noti_background.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_new_ticket_details.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_new_ticket_details.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_spinner_item.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/spinner_item.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_application_icon.png.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/application_icon.png"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_ic_launcher_background.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/xml_backup_rules.xml.flat", "source": "com.example.standardtemplate.app-main-47:/xml/backup_rules.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_login.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_login.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_main.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_main.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_barcode_applet.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_barcode_applet.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_accepted_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_accepted_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_recycle_view_new_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/recycle_view_new_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_radio_button_red.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/radio_button_red.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/xml_file_provider_paths.xml.flat", "source": "com.example.standardtemplate.app-main-47:/xml/file_provider_paths.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_speech_recognizer.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_speech_recognizer.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_action_button.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_action_button.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_dialog_background.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/dialog_background.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_new_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_new_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_register.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_register.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_input_field_background.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/input_field_background.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_item_accepted_ticket_list.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/item_accepted_ticket_list.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_radio_button_unchecked.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/radio_button_unchecked.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/drawable_rounded_background_light.xml.flat", "source": "com.example.standardtemplate.app-main-47:/drawable/rounded_background_light.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_movie_applet.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_movie_applet.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_dialog_confirmation.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/dialog_confirmation.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_document_applet.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_document_applet.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_dialog_create.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/dialog_create.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/xml_data_extraction_rules.xml.flat", "source": "com.example.standardtemplate.app-main-47:/xml/data_extraction_rules.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/xml_locales_config.xml.flat", "source": "com.example.standardtemplate.app-main-47:/xml/locales_config.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_dialog_message.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/dialog_message.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_microphone_release.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_microphone_release.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_audio_capture.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_audio_capture.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_dialog_setting.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/dialog_setting.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_text_to_speech.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_text_to_speech.xml"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.standardtemplate.app-main-47:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.standardtemplate.app-release-46:/layout_activity_realwear_help_menu.xml.flat", "source": "com.example.standardtemplate.app-main-47:/layout/activity_realwear_help_menu.xml"}]
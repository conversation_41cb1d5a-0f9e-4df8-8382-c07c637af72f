<lint-module
    format="1"
    dir="D:\MobileProject\standardtemplate-kotlin\app"
    name=":app"
    type="APP"
    maven="Standard Template:app:unspecified"
    agpVersion="8.11.1"
    buildFolder="build"
    bootClassPath="D:\AndroidSDK\platforms\android-35\android.jar;D:\AndroidSDK\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>

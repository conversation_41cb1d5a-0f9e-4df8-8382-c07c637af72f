package com.example.standardtemplate.Activities.NewTickets

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.R
import org.w3c.dom.Text
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class NewTicketDetailsActivity : BaseActivity() {
    private lateinit var btnAccept : Button
    private lateinit var btnBack : Button
    private lateinit var txtMachineId: TextView
    private lateinit var txtMachineStatus: TextView
    private lateinit var txtErrorMsg: TextView
    private lateinit var txtCreatedAt: TextView
    private lateinit var txtCreator: TextView
    private lateinit var txtStatus: TextView
    private lateinit var txtMachineDowntime : TextView

    private val token = UserInfoManager.token.toString()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge()
        setContentView(R.layout.activity_new_ticket_details)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        //get all the information that passed from the NewTicketAdapter
        val id = intent.getStringExtra("id")
        val creator = intent.getStringExtra("creator")
        val status = intent.getStringExtra("status")
        val createdAt = intent.getStringExtra("createdAt")
        val machineId = intent.getStringExtra("machineId")
        val errorMsg = intent.getStringExtra("errorMsg")
        val machineStatus = intent.getStringExtra("machineStatus")
        val machineDowntime = intent.getStringExtra("machineDowntime")

        txtMachineId = findViewById(R.id.txtMachineId)
        txtMachineStatus = findViewById(R.id.txtMachineStatus)
        txtErrorMsg = findViewById(R.id.txtErrorMsg)
        txtCreatedAt = findViewById(R.id.txtCreatedAt)
        txtCreator = findViewById(R.id.txtCreator)
        txtStatus = findViewById(R.id.txtStatus)
        txtMachineDowntime = findViewById(R.id.txtMachineDowntime)

        txtMachineId.text = getString(R.string.txtmachineId)+ "$machineId"
        txtMachineStatus.text = getString(R.string.machStatus) + "$machineStatus"
        txtErrorMsg.text = getString(R.string.txtErrorMsg) +"$errorMsg"
        txtCreatedAt.text = getString(R.string.txtCreatedAt) + "$createdAt"
        txtCreator.text = getString(R.string.txtCreator) + "$creator"
        txtStatus.text = getString(R.string.txtStatus) + "$status"
        txtMachineDowntime.text = getString(R.string.txtMachDowntime) +"$machineDowntime"

        btnBack = findViewById(R.id.btnBack)
        btnBack.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }

        /*
        handle the btnAccept request by calling the acceptTicket function
        if the return success, the back to the last activity
        else update log and show a message dialog with title 'Accept Failed'
         */
        btnAccept = findViewById(R.id.btnAccept)
        btnAccept.setOnClickListener {
            // Handle accept button click
            acceptTicket(id!!) { success ->
                if (success) {
                    onBackPressedDispatcher.onBackPressed()
                    finish()
                } else {
                    Log.e("UPDATE TICKET", "Ticket update failed")
                    showMessageDialog(this, getString(R.string.failed), getString(R.string.failedMsg))
                }
            }
        }
    }

    /**
     * function to let the user to accept ticket
     * @param id the id of the ticket
     * @param callback the callback function to handle the response
     * @return the response from the server, if success return true, else return false
     */
    private fun acceptTicket(id: String,  callback: (Boolean) -> Unit) {
        if (token.isEmpty()){
            Toast.makeText(this@NewTicketDetailsActivity, "Token is null", Toast.LENGTH_SHORT).show()
        }
        val apiService = ApiClient.getAuthenticatedClient(this, token).create(ApiInterface::class.java)

        apiService.acceptTicket(id.toInt()).enqueue(object : Callback<Void> {
            override fun onResponse(call: Call<Void>, response: Response<Void>) {
                if (response.isSuccessful) {
                    Log.d("UPDATE TICKET", "Ticket Updated")

                    showMessageDialog(
                        this@NewTicketDetailsActivity,
                        getString(R.string.success),
                        getString(R.string.updateSuccess)
                    ) {
                        callback(true) // Call the callback function with success
                    }
                } else {
                    showMessageDialog(
                        this@NewTicketDetailsActivity,
                        getString(R.string.AT_001),
                        getString(R.string.errorCode_AT_001)
                    ) {
                        callback(false) // Call the callback function with failure
                    }
                }
            }

            override fun onFailure(call: Call<Void>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()
                callback(false)
            }
        })
    }

}
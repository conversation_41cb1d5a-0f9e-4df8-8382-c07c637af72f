package com.example.standardtemplate.Activities.AcceptedTicket

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter
import com.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.R
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class AcceptedTicketListActivity : AppCompatActivity() {
    private lateinit var btnReturn: Button
    private lateinit var rvAcceptedTicketList: RecyclerView
    private lateinit var acceptedTicketAdapter: AcceptedTicketAdapter
    private lateinit var sharedPref: SharedPref
    private lateinit var apiService: ApiInterface

    private val acceptedTicketList: MutableList<AcceptedTicketDto> = mutableListOf()
    private val token = UserInfoManager.token.toString()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_accepted_ticket_list)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        sharedPref = SharedPref(this@AcceptedTicketListActivity)
        // Handle Return Button
        btnReturn = findViewById(R.id.btnReturn)
        btnReturn.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        getAcceptedTicketList()
    }

    private fun getAcceptedTicketList() {
        Log.i("token in accepted list", "$token")
        apiService = ApiClient.getAuthenticatedClient(this,token).create(ApiInterface::class.java)

        rvAcceptedTicketList = findViewById(R.id.rvAcceptedTicketList)
        rvAcceptedTicketList.layoutManager = GridLayoutManager(this, 2)

        acceptedTicketAdapter = AcceptedTicketAdapter(this,acceptedTicketList, username = UserInfoManager.username.toString())
        rvAcceptedTicketList.adapter = acceptedTicketAdapter

        // Fetch Data from API
        fetchAcceptedTickets()
    }

    private fun fetchAcceptedTickets() {
        apiService.getAcceptedTickets().enqueue(object : Callback<List<AcceptedTicketDto>> {
            override fun onResponse(call: Call<List<AcceptedTicketDto>>, response: Response<List<AcceptedTicketDto>>) {
                Log.i("response code", "Fetch Accept code: ${response.code()}")
                if (response.isSuccessful) {
                    response.body()?.let {
                        acceptedTicketList.clear()
                        acceptedTicketList.addAll(it)
                        acceptedTicketAdapter.notifyDataSetChanged()
                    }
                } else {
                    Toast.makeText(this@AcceptedTicketListActivity, "Failed to load tickets", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<List<AcceptedTicketDto>>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()            }
        })
    }
}

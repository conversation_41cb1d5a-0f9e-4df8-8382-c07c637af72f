<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activities.AcceptedTicket.AcceptedTicketListActivity"
    android:padding="30dp"
    android:background="@color/white">

    <!-- Title -->
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Accpeted Ticket List"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textColor="@color/primaryColor"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="10dp"/>

    <!-- RecyclerView for Users Table -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvAcceptedTicketList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:scrollbars="vertical"
        android:overScrollMode="always"
        app:layout_constraintTop_toBottomOf="@id/txtTitle"
        app:layout_constraintBottom_toTopOf="@id/btnReturn"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"/>

    <Button
        android:id="@+id/btnReturn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/rvAcceptedTicketList"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="Back"
        android:backgroundTint="@color/primaryDarkColor"

        android:layout_margin="20dp"
        />

</androidx.constraintlayout.widget.ConstraintLayout>

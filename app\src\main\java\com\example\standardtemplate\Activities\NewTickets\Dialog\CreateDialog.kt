package com.example.standardtemplate.Activities.NewTickets.Dialog

import android.app.Dialog
import android.view.LayoutInflater
import android.content.Context
import android.view.Window
import android.widget.Button
import android.widget.EditText
import com.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListener
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

class CreateDialog(private val context: Context, private val listener: CreateDialogListener) {
    /**
     * show the create Ticket dialog to the user
     * @param context the context of the activity
     * @return the dialog that is created and pass the date to NewTicketListActivity when the submit button is clicked using listener
     */
    fun showCreateDialog(context : Context) {
        val dialog = Dialog(context)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_create, null)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(view)

        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val editMachineId = view.findViewById<EditText>(R.id.editMachineId)
        val editMachineStatus = view.findViewById<EditText>(R.id.editMachineStatus)
        val editErrorMsg = view.findViewById<EditText>(R.id.editErrorMsg)

        val btnCancel = view.findViewById<Button>(R.id.btnCancel)
        val btnSubmit = view.findViewById<Button>(R.id.btnSubmit)

        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnSubmit.setOnClickListener {
            val token = UserInfoManager.token.toString()

            if (token.isEmpty()) {
                showMessageDialog(context, "Error", "Authentication failed! Please log in again.")
                return@setOnClickListener
            }

            val id = editMachineId.text.toString()
            val status = editMachineStatus.text.toString()
            val error = editErrorMsg.text.toString()

            if (id.isNotEmpty() && status.isNotEmpty() && error.isNotEmpty()) {
                val downtime = getCurrentFormattedDate()

                // pass the information to the newTicketListActivity to processed the data
                listener.onSubmitTicket(id, status, error, downtime)

                dialog.dismiss() // Only dismiss if everything is valid
            }else{
                showMessageDialog(context, context.getString(R.string.reminder), context.getString(R.string.reminderMsg2))
            }
        }

        dialog.show()
    }

    // Helper function to get the current date and time in the desired format
    fun getCurrentFormattedDate(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS", Locale.getDefault())
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")
        return dateFormat.format(Date())
    }
}

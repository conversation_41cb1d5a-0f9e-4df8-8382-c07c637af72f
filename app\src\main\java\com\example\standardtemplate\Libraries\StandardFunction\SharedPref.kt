package com.example.standardtemplate.Libraries.StandardFunction

import android.content.Context
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Models.LoginResponse
import com.example.standardtemplate.Models.UserDetails
import com.google.gson.Gson

class SharedPref(context: Context) {
    /**
     *  saveUserData --> save the user data into the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param user the user data, passed from the getUserData function
     */
    fun saveUserData(context: Context, user: UserDetails) {
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("AuthPrefs", Context.MODE_PRIVATE).edit()
        val gson = Gson()
        val userJson = gson.toJson(user) //convert user object into gson format
        sharedPref.putString("user_info", userJson)
        sharedPref.apply()
        sharedPref.commit()
    }

    /**
     *  getUserData --> get the user data from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param attribute the attribute of the user data, which stored in variable, passed from the getUserData function
     *  @return the value of the attribute that user entered, for example : getUserData(this,"username") will return the username stored
     */
    fun getUserData(context: Context, attribute: String): String? {
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("AuthPrefs", Context.MODE_PRIVATE)
        val gson = Gson()
        val userJson = sharedPref.getString("user_info", null)?: return null
        val user = gson.fromJson(userJson, UserDetails::class.java)
        return when(attribute){
            "username" -> user.username
            "id" -> user.id.toString()
            "fullname" -> user.fullname
            else -> null
        }
    }

    /**
     *  saveToken --> save the token and expiration retrieve from the API into the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param authentication the token and expiration, passed from the login function
     */
    fun saveToken(context:Context, authentication: LoginResponse) {
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("AuthPrefs", Context.MODE_PRIVATE).edit()
        val gson = Gson()
        val loginJson = gson.toJson(authentication)
        sharedPref.putString("authentication", loginJson)
        sharedPref.apply()
        sharedPref.commit()
    }

    /**
     *  getAuthentication --> get the token and expiration from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @return the value of the attribute that user entered, for example : getAuthentication(this,"token") will return the token stored
     */
    fun getAuthentication(context: Context): String? {
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("AuthPrefs", Context.MODE_PRIVATE)
        val gson = Gson()
        val authJson = sharedPref.getString("authentication", null) ?: return null
        val authData = gson.fromJson(authJson, LoginResponse::class.java)
        return authData.token
    }

    /**
     *  clearUserData --> clear the user data from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     */
    fun clearUserData(context: Context){ //used to clear all the user data inside sharedPreferences file when it is not needed, for example: after the user has been logged out
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("AuthPrefs", Context.MODE_PRIVATE).edit()
        sharedPref.clear()
        sharedPref.apply()
        sharedPref.commit()
    }

    /**
     *  getLastVisit --> get the last visit date of specific user from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param username the username of the user, passed from the context
     */
    fun getLastVisit(context: Context,username: String): String {
        ApiClient.clearVals()
        val sharedprefs = context.getSharedPreferences("TicketMonitoring", Context.MODE_PRIVATE)

        // Retrieve last visit for the specific user
        return sharedprefs.getString("lastVisit_$username", "Never") ?: "Never"
    }

    /**
     *  saveLastVisit --> save the last visit date of specific user into the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param username the username of the user, passed from the context
     *  @param lastVisit the last visit date, passed from the startMonitoringService function
     */
    fun saveLastVisit(context: Context, username: String, lastVisit: String) {
        ApiClient.clearVals()
        val ticket =context.getSharedPreferences("TicketMonitoring", Context.MODE_PRIVATE).edit()

        if (username != null) {
            ticket.putString("lastVisit_${username}", lastVisit)
        }

        ticket.apply()
        ticket.commit()
    }

    /**
     *  saveBaseUrl --> save the url insert by the user into the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param baseUrl the url insert by the user, passed from the settingDialog
     */
    fun saveBaseUrl(context: Context, baseUrl: String) {
        ApiClient.clearVals()
        val sharedPreferences = context.getSharedPreferences("URLConnection", Context.MODE_PRIVATE)
        sharedPreferences.edit().putString("BASE_URL", baseUrl).apply()
    }

    /**
     *  getBaseUrl --> get the url from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @param baseUrl the url insert by the user, passed from the settingDialog
     */
    fun getBaseUrl(context: Context): String {
        val sharedPreferences = context.getSharedPreferences("URLConnection", Context.MODE_PRIVATE)
        return sharedPreferences.getString("BASE_URL", "")!!
    }

    /**
     * save language --> save the language insert by the user into the sharedPreference
     * @param context the context of the activity, passed from the loginActivity
     * @param languageCode the language select by the user, passed from the settingDialog
     */
    fun saveLanugage(context: Context, languageCode:String) {
        ApiClient.clearVals()
        val sharedPreferences = context.getSharedPreferences("appSetting", Context.MODE_PRIVATE)
        sharedPreferences.edit().putString("locale", languageCode).apply()
    }

    /**
     *  getLanguage --> get the language from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     */
    fun getLanguage(context:Context): String? {
        ApiClient.clearVals()
        val sharedPreferences = context.getSharedPreferences("appSetting", Context.MODE_PRIVATE)
        return sharedPreferences.getString("locale", "en")
    }

    /**
     *  recordLastVisit --> used to save the last visit for this application
     *  @param context the context of the activity, passed from the loginActivity
     *  @param lastVisit the last visit date in string format, passed from the startMonitoringService function
     */
    fun recordLastVisit(context : Context, lastVisit: String){
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("appSetting", Context.MODE_PRIVATE).edit()
        sharedPref.putString("lastVisit", lastVisit)
        sharedPref.apply()
    }


    /**
     *  getLastVisitRecord --> get the last visit date from the sharedPreference
     *  @param context the context of the activity, passed from the loginActivity
     *  @return the last visit date in string format
     */
    fun getLastVisitRecord(context: Context): String?{
        ApiClient.clearVals()
        val sharedPref = context.getSharedPreferences("appSetting", Context.MODE_PRIVATE)
        return sharedPref.getString("lastVisit", "")
    }

}
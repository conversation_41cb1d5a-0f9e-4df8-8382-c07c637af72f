package com.example.standardtemplate.Libraries.LanguageSetting

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Libraries.TicketMonitoring
import com.example.standardtemplate.R
import retrofit2.Call
import retrofit2.Callback
import java.util.Locale

open class BaseActivity : AppCompatActivity() {
    private lateinit var sharedPref : SharedPref

    companion object {
        var dLocale: Locale? = null
    }

    override fun attachBaseContext(newBase: Context) {
        ApiClient.clearVals()
        sharedPref = SharedPref(this)

        val languageCode = sharedPref.getLanguage(newBase)?:""
        Log.d("language code", languageCode)

        val locale = Locale(languageCode)
        val config = Configuration(newBase.resources.configuration)
        config.setLocale(locale)

        val context = newBase.createConfigurationContext(config)
        super.attachBaseContext(context)
    }

    override fun onResume() {
        super.onResume()
        // Ensure the locale is correct when coming back to this activity
        sharedPref = SharedPref(this)

        val languageCode = sharedPref.getLanguage(this)?:""
        Log.d("language code", languageCode)

        if (dLocale?.language != languageCode) {
            dLocale = Locale(languageCode)
            recreate()  // 🔹 Restart the activity to apply the locale
        }
    }

    /*
    start monitoring service
     */
    fun startMonitoringService(context: Context, lastVisit: String) {
        val intent = Intent(context, TicketMonitoring::class.java)
        intent.putExtra("LAST_VISIT", lastVisit) // Pass last visit date
        ContextCompat.startForegroundService(context, intent)
    }

    /*
    stop monitoring service
     */
    fun stopMonitoringService(context: Context) {
        val intent = Intent(context, TicketMonitoring::class.java)
        context.stopService(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
//        stopMonitoringService(this)
    }
}

RealWear Sample Video File (MP4)

This is a placeholder for a sample MP4 video file used in the RealWear Movie Applet demonstration.

In a real implementation, this would be a proper MP4 video file.

For testing purposes, you can replace this file with an actual video:
- Copy a real MP4 video to app/src/main/assets/sample_video.mp4
- The MovieAppletActivity will automatically use it
- The video will be copied to external storage and opened in the system video player

Video specifications for RealWear devices:
✓ Recommended format: MP4 (H.264/AVC video, AAC audio)
✓ Resolution: 720p or 1080p
✓ Bitrate: 1-5 Mbps for optimal performance
✓ Frame rate: 24-30 fps

Features demonstrated:
✓ File copying from assets to external storage
✓ FileProvider integration for secure file access
✓ Intent-based video playback
✓ External video player integration
✓ Multiple video format support

RealWear Development Software
(c) RealWear, Inc. All rights reserved.


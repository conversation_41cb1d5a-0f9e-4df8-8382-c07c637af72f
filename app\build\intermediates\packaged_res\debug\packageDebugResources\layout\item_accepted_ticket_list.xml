<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:id="@+id/cardView"
    android:background="@color/white"
    android:clickable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/transparent">

        <TextView
            android:id="@+id/txtMachineId"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="machine"
            android:textSize="16sp"
            android:textColor="@color/primaryColor"
            android:textStyle="bold"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray"
            android:layout_marginVertical="8dp"/>

        <TextView
            android:id="@+id/txtErrorMsg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/textColor"
            android:text="system down"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/txtMachineStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/textColor"
            android:text="DOWN"
            android:textSize="14sp"
            />

        <TextView
            android:id="@+id/txtAcceptedby"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/textColor"
            android:text="Accepted by"
            android:textSize="14sp"
            />
    </LinearLayout>
</androidx.cardview.widget.CardView>

R_DEF: Internal format may change without notice
local
color accentColor
color accent_color
color background_light
color black
color blue
color dark_gray
color divider
color green
color hintColor
color light_gray
color orange
color primaryColor
color primaryDarkColor
color primary_color
color primary_dark_color
color purple
color red
color textColor
color text_secondary
color white
color yellow
drawable application_icon
drawable dialog_background
drawable ic_back
drawable ic_close
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_noti_background
drawable input_field_background
drawable radio_button_red
drawable radio_button_unchecked
drawable rounded_background_light
id actionButtonImageView
id barcode_textview
id bnfDescriptionView
id btnAccept
id btnAcceptedTicketList
id btnActionButton
id btnAddCommands
id btnAudioCapture
id btnBNFGrammar
id btnBack
id btnBackToMain
id btnBarcodeApplet
id btnBasicVideo
id btnBitmapPhoto
id btnCameraApplet
id btnCancel
id btnClearCommands
id btnClearGrammar
id btnClearHistory
id btnClearResults
id btnClearText
id btnClose
id btnConfirm
id btnCreate
id btnDocumentViewer
id btnDone
id btnFileProviderPhoto
id btnFileProviderVideo
id btnHelpMenu
id btnHideKeyboard
id btnKeyboardDictation
id btnLaunchAviVideo
id btnLaunchDictation
id btnLaunchImageDocument
id btnLaunchKeyboard
id btnLaunchMp4Video
id btnLaunchPdfDocument
id btnLogin
id btnLogout
id btnMicrophoneRelease
id btnMovieApplet
id btnOk
id btnRealWear
id btnRealWearDev
id btnRefreshCommands
id btnRegister
id btnReleaseMicrophone
id btnRestoreMicrophone
id btnReturn
id btnSave
id btnScanAllFormats
id btnScanBarcode
id btnScanQROnly
id btnSetNumberGrammar
id btnSetTimeGrammar
id btnSetting
id btnSpeakSample
id btnSpeakText
id btnSpeechRecognition
id btnSubmit
id btnTextToSpeech
id camera_image_view
id cardView
id commandHistoryView
id commandsView
id dictationField
id dlCreateTicket
id editErrorMsg
id editMachineId
id editMachineStatus
id edtRemedy
id edtUrl
id errorMsgLayout
id fileTextLabel
id fullNameLayout
id historyView
id hlLogin
id hlRegister
id linearLayout
id machineIdLayout
id machineStatusLayout
id main
id monoButton
id passwordLayout
id playbackButton
id progressBar
id quantityView
id rate16Button
id rate44Button
id rate48Button
id rate8Button
id recordButton
id resultsView
id rvAcceptedTicketList
id rvTicketList
id spinner
id spinnerText
id statusLabel
id statusView
id stereoButton
id switchCustomMessage
id switchHideText
id textInput
id ticketCardView
id tvAttendBy
id tvCompleteAt
id tvCreatedAt
id tvErrorMsg
id tvMachineId
id tvMachineStatus
id tvMessage
id tvStatus
id tvTicketId
id tvTitle
id txtAcceptedby
id txtCreatedAt
id txtCreator
id txtErrorMsg
id txtFullName
id txtHeader
id txtInstructions
id txtMachineDowntime
id txtMachineId
id txtMachineStatus
id txtPass
id txtPassword
id txtRegion
id txtStatus
id txtTitle
id txtURLConnection
id txtUsername
id urlInputLayout
id usernameLayout
layout activity_accepted_ticket_details
layout activity_accepted_ticket_list
layout activity_login
layout activity_new_ticket_details
layout activity_new_ticket_list
layout activity_realwear_action_button
layout activity_realwear_audio_capture
layout activity_realwear_barcode_applet
layout activity_realwear_bnf_grammar
layout activity_realwear_camera_applet
layout activity_realwear_document_applet
layout activity_realwear_help_menu
layout activity_realwear_keyboard_dictation
layout activity_realwear_main
layout activity_realwear_microphone_release
layout activity_realwear_movie_applet
layout activity_realwear_speech_recognizer
layout activity_realwear_text_to_speech
layout activity_register
layout dialog_confirmation
layout dialog_create
layout dialog_message
layout dialog_setting
layout item_accepted_ticket_list
layout recycle_view_new_ticket_list
layout spinner_item
mipmap ic_launcher
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string AT_001
string CT_001
string C_001
string ConnectionError
string DT_001
string ErrorMsg
string F_001
string L_001
string L_002
string L_003
string R_001
string U_001
string UrlConnection
string acceptedTicketList
string actionStr
string app_name
string btnAccept
string btnBack
string btnCreate
string btnLogin
string btnRegister
string cancel
string change_language
string change_language_content1
string change_language_content2
string chgLanguage
string chinese
string close
string confirm
string confirmationStr
string connectionFailed
string connectionFailedMsg
string createSuccess
string createTicket
string english
string errorCode_AT_001
string errorCode_CT_001
string errorCode_C_001
string errorCode_DT_001
string errorCode_F_001
string errorCode_L_001
string errorCode_L_002
string errorCode_L_003
string errorCode_R_001
string errorCode_U_001
string exit
string exitMsg
string failed
string failedMsg
string fullname
string gcm_defaultSenderId
string getUserFailed
string getUserFailedMsg
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string hlLogin
string hlRegister
string insUrlCon
string language
string language_chinese
string language_english
string loginFailed
string loginFailedMsg
string loginSuc
string logout
string logoutMsg
string machStatus
string machineID
string machineStat
string newTicketTitle
string pass
string proceedLogin
string project_id
string realwear_action_button_description
string realwear_action_button_instructions
string realwear_action_button_status
string realwear_action_button_status_text
string realwear_action_button_title
string realwear_audio_16khz
string realwear_audio_44khz
string realwear_audio_48khz
string realwear_audio_8khz
string realwear_audio_back
string realwear_audio_channels_section
string realwear_audio_description
string realwear_audio_file_create_error
string realwear_audio_file_write_error
string realwear_audio_filename_placeholder
string realwear_audio_instructions
string realwear_audio_instructions_title
string realwear_audio_mono
string realwear_audio_no_recording
string realwear_audio_output_section
string realwear_audio_permissions_denied
string realwear_audio_permissions_error
string realwear_audio_permissions_granted
string realwear_audio_playback
string realwear_audio_playback_complete
string realwear_audio_playback_failed
string realwear_audio_playing
string realwear_audio_ready
string realwear_audio_record
string realwear_audio_record_complete
string realwear_audio_record_failed
string realwear_audio_record_init_failed
string realwear_audio_recording
string realwear_audio_sample_rate_section
string realwear_audio_status_label
string realwear_audio_status_ready
string realwear_audio_status_section
string realwear_audio_stereo
string realwear_audio_title
string realwear_back_to_main
string realwear_back_to_main_msg
string realwear_barcode_applet_description
string realwear_barcode_applet_title
string realwear_barcode_characters
string realwear_barcode_clear_results
string realwear_barcode_instructions
string realwear_barcode_instructions_title
string realwear_barcode_no_data
string realwear_barcode_ready_to_scan
string realwear_barcode_result_label
string realwear_barcode_result_length
string realwear_barcode_results_title
string realwear_barcode_scan_all_formats
string realwear_barcode_scan_cancelled
string realwear_barcode_scan_default
string realwear_barcode_scan_failed
string realwear_barcode_scan_options
string realwear_barcode_scan_qr_only
string realwear_barcode_scan_success
string realwear_barcode_scan_time
string realwear_barcode_scanner_not_available
string realwear_bnf_back
string realwear_bnf_clear_grammar
string realwear_bnf_clear_history
string realwear_bnf_command_history
string realwear_bnf_command_received
string realwear_bnf_command_toast
string realwear_bnf_description
string realwear_bnf_grammar_clear_failed
string realwear_bnf_grammar_cleared
string realwear_bnf_grammar_section
string realwear_bnf_grammar_set_failed
string realwear_bnf_history_cleared
string realwear_bnf_history_section
string realwear_bnf_instructions
string realwear_bnf_instructions_title
string realwear_bnf_listening
string realwear_bnf_no_commands
string realwear_bnf_number_grammar_set
string realwear_bnf_ready
string realwear_bnf_set_number_grammar
string realwear_bnf_set_time_grammar
string realwear_bnf_status_format
string realwear_bnf_status_ready
string realwear_bnf_status_section
string realwear_bnf_time_grammar_set
string realwear_bnf_title
string realwear_camera_applet_description
string realwear_camera_applet_title
string realwear_camera_basic_video
string realwear_camera_bitmap_photo
string realwear_camera_coming_soon
string realwear_camera_file_provider_photo
string realwear_camera_file_provider_video
string realwear_camera_photo_section
string realwear_camera_preview_description
string realwear_camera_preview_instruction
string realwear_camera_preview_label
string realwear_camera_title
string realwear_camera_video_section
string realwear_dev_entrance
string realwear_dev_entrance_desc
string realwear_document_applet_description
string realwear_document_applet_title
string realwear_document_coming_soon
string realwear_document_copy_failed
string realwear_document_file_not_found
string realwear_document_image_description
string realwear_document_image_section
string realwear_document_instructions
string realwear_document_instructions_title
string realwear_document_open_image
string realwear_document_open_pdf
string realwear_document_pdf_description
string realwear_document_pdf_section
string realwear_document_title
string realwear_document_viewer_not_available
string realwear_help_add_commands
string realwear_help_add_failed
string realwear_help_back
string realwear_help_clear_commands
string realwear_help_clear_failed
string realwear_help_command_back
string realwear_help_command_help
string realwear_help_command_home
string realwear_help_command_navigate
string realwear_help_command_playback
string realwear_help_command_record
string realwear_help_command_select
string realwear_help_command_settings
string realwear_help_commands_active
string realwear_help_commands_added
string realwear_help_commands_cleared
string realwear_help_commands_list
string realwear_help_commands_refreshed
string realwear_help_commands_section
string realwear_help_control_section
string realwear_help_description
string realwear_help_instructions
string realwear_help_instructions_title
string realwear_help_no_commands
string realwear_help_ready
string realwear_help_refresh_commands
string realwear_help_refresh_failed
string realwear_help_status_label
string realwear_help_status_ready
string realwear_help_status_section
string realwear_help_title
string realwear_keyboard_dictation_cancelled
string realwear_keyboard_dictation_clear
string realwear_keyboard_dictation_description
string realwear_keyboard_dictation_failed
string realwear_keyboard_dictation_hide_keyboard
string realwear_keyboard_dictation_hint
string realwear_keyboard_dictation_input_methods
string realwear_keyboard_dictation_instructions
string realwear_keyboard_dictation_keyboard_error
string realwear_keyboard_dictation_keyboard_hidden
string realwear_keyboard_dictation_keyboard_input
string realwear_keyboard_dictation_keyboard_shown
string realwear_keyboard_dictation_launching
string realwear_keyboard_dictation_no_text
string realwear_keyboard_dictation_not_available
string realwear_keyboard_dictation_ready
string realwear_keyboard_dictation_status_label
string realwear_keyboard_dictation_status_ready
string realwear_keyboard_dictation_success
string realwear_keyboard_dictation_text_cleared
string realwear_keyboard_dictation_text_input
string realwear_keyboard_dictation_title
string realwear_keyboard_dictation_voice_input
string realwear_main_button
string realwear_main_description
string realwear_main_title
string realwear_microphone_back
string realwear_microphone_clear_history
string realwear_microphone_custom_message
string realwear_microphone_custom_message_text
string realwear_microphone_description
string realwear_microphone_hide_text
string realwear_microphone_history_cleared
string realwear_microphone_history_empty
string realwear_microphone_history_title
string realwear_microphone_instructions
string realwear_microphone_instructions_title
string realwear_microphone_listening
string realwear_microphone_options_section
string realwear_microphone_processing_error
string realwear_microphone_ready
string realwear_microphone_register_failed
string realwear_microphone_release
string realwear_microphone_release_failed
string realwear_microphone_released
string realwear_microphone_releasing
string realwear_microphone_restore
string realwear_microphone_restore_failed
string realwear_microphone_restoring
string realwear_microphone_status_label
string realwear_microphone_status_ready
string realwear_microphone_status_section
string realwear_microphone_system_request
string realwear_microphone_title
string realwear_movie_applet_description
string realwear_movie_applet_title
string realwear_movie_avi_description
string realwear_movie_avi_section
string realwear_movie_copy_failed
string realwear_movie_file_not_found
string realwear_movie_instructions
string realwear_movie_instructions_title
string realwear_movie_mp4_description
string realwear_movie_mp4_section
string realwear_movie_play_avi
string realwear_movie_play_mp4
string realwear_movie_player_not_available
string realwear_movie_preview_description
string realwear_movie_preview_instruction
string realwear_movie_preview_title
string realwear_speech_coming_soon
string realwear_speech_recognizer_back
string realwear_speech_recognizer_clear_results
string realwear_speech_recognizer_command_received
string realwear_speech_recognizer_commands_registered
string realwear_speech_recognizer_current_command
string realwear_speech_recognizer_description
string realwear_speech_recognizer_instructions
string realwear_speech_recognizer_instructions_title
string realwear_speech_recognizer_listening
string realwear_speech_recognizer_no_command
string realwear_speech_recognizer_no_command_yet
string realwear_speech_recognizer_processing_error
string realwear_speech_recognizer_ready
string realwear_speech_recognizer_ready_message
string realwear_speech_recognizer_register_failed
string realwear_speech_recognizer_results_cleared
string realwear_speech_recognizer_results_title
string realwear_speech_recognizer_setup_failed
string realwear_speech_recognizer_status_commands_received
string realwear_speech_recognizer_status_label
string realwear_speech_recognizer_status_ready
string realwear_speech_recognizer_status_section
string realwear_speech_recognizer_title
string realwear_speech_title
string realwear_status_info
string realwear_tts_back
string realwear_tts_clear_history
string realwear_tts_description
string realwear_tts_empty_text
string realwear_tts_empty_text_status
string realwear_tts_finished
string realwear_tts_history_cleared
string realwear_tts_history_empty
string realwear_tts_history_title
string realwear_tts_input_hint
string realwear_tts_input_section
string realwear_tts_instructions
string realwear_tts_instructions_title
string realwear_tts_processing_error
string realwear_tts_ready
string realwear_tts_register_failed
string realwear_tts_sample_text
string realwear_tts_service_ready
string realwear_tts_speak_sample
string realwear_tts_speak_text
string realwear_tts_speaking
string realwear_tts_start_failed
string realwear_tts_status_label
string realwear_tts_status_ready
string realwear_tts_status_section
string realwear_tts_title
string region
string register
string reminder
string reminderMsg
string reminderMsg2
string save
string select_language
string setting
string success
string txtCreatedAt
string txtCreator
string txtErrorMsg
string txtMachDowntime
string txtStatus
string txtmachineId
string updConnection
string updateSuccess
string username
string welcMsg
string welcome
style Base.Theme.StandardTemplate
style Theme.StandardTemplate
xml backup_rules
xml data_extraction_rules
xml file_provider_paths
xml locales_config

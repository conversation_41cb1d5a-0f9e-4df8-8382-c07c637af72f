<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activities.AcceptedTicket.AcceptedTicketDetailsActivity"
    android:background="@color/white"
    android:padding="10dp"
    >

    <!-- Title -->
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="View Accepted Ticket Details"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textAlignment="center"
        android:textColor="@color/primaryColor"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="10dp"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/ticketCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@android:color/white"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:padding="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="20dp"
            android:padding="16dp">

            <!-- First Section -->
            <TextView
                android:id="@+id/tvTicketId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Ticket ID: "
                android:textStyle="bold"
                android:padding="5dp"/>

            <TextView
                android:id="@+id/tvMachineId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Machine ID: "
                android:padding="5dp"/>

            <TextView
                android:id="@+id/tvErrorMsg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Error Message: "
                android:padding="5dp"/>

            <TextView
                android:id="@+id/tvMachineStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Machine Status: "
                android:padding="5dp"/>

            <TextView
                android:id="@+id/txtMachineDowntime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Machine Downtime"
                android:textSize="14sp"
                android:layout_marginTop="3dp"
                android:padding="4dp"/>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray"
                android:layout_marginVertical="8dp"/>

            <!-- Second Section -->
            <TextView
                android:id="@+id/tvCreatedAt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Created At: "
                android:padding="5dp"/>

            <TextView
                android:id="@+id/tvAttendBy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Attended By: "
                android:padding="5dp"/>

            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Status: "
                android:padding="5dp"/>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray"
                android:layout_marginVertical="8dp"/>

            <!-- Third Section -->
            <TextView
                android:id="@+id/tvCompleteAt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Remedy:"
                android:padding="5dp"/>

            <EditText
                android:id="@+id/edtRemedy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="(Key in here)"
                android:padding="5dp"
                android:textSize="14sp"
                android:clickable="false"
                android:background="@null"
                />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/btnDone"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="24dp"
        android:text="DONE"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:backgroundTint="@color/primaryColor"
        app:layout_constraintTop_toBottomOf="@id/ticketCardView"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginRight="10dp"
        app:layout_constraintStart_toEndOf="@+id/btnBack"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="4dp"/>

    <Button
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="24dp"
        android:text="Back"
        android:textSize="16sp"
        android:textColor="@color/white"
        android:backgroundTint="@color/primaryDarkColor"
        app:layout_constraintTop_toBottomOf="@+id/ticketCardView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnDone"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginLeft="10dp"
        android:padding="4dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>

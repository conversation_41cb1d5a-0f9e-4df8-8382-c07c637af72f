<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="20dp"
    android:background="@color/white"
    tools:context=".Activities.Login_Setting.LoginActivity">

    <!-- Welcome Text -->
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:padding="16dp"
        android:text="@string/welcome"
        android:textColor="@color/primaryColor"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Username Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/usernameLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        app:boxStrokeColor="@color/primaryColor"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/hintColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtUsername"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/username"
            android:text="admin"
            android:inputType="text"
            android:textColor="@color/textColor"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password Field -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/passwordLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        app:boxStrokeColor="@color/primaryColor"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/hintColor"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/usernameLayout">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/txtPass"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/pass"
            android:text="admin"
            android:inputType="textPassword"
            android:textColor="@color/textColor"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Login Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLogin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="24dp"
        android:backgroundTint="@color/primaryColor"
        android:text="@string/btnLogin"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/passwordLayout" />

    <!-- Register Hyperlink -->
    <TextView
        android:id="@+id/hlRegister"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        android:text="@string/hlRegister"
        android:textAlignment="textEnd"
        android:textColor="@color/primaryColor"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

    <!-- RealWear开发者入口按钮 - 移到显眼位置 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnRealWearDev"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="24dp"
        android:backgroundTint="@color/primaryColor"
        android:text="🔧 RealWear Development"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        app:cornerRadius="12dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hlRegister" />

    <!-- Settings Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnSetting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/primaryDarkColor"
        android:text="@string/setting"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnRealWearDev" />

    <!-- Language Selection Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnSetting">

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <TextView
                android:id="@+id/txtRegion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:text="@string/language"
                android:textColor="@color/textColor"
                android:textSize="16sp" />

<!--            drop down list for language -->
            <Spinner
                android:id="@+id/spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:foregroundTint="@color/white"
                android:dropDownWidth="100dp"
                android:spinnerMode="dropdown"
                android:layout_weight="1" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>



</androidx.constraintlayout.widget.ConstraintLayout>
#Tue Aug 19 15:30:52 MYT 2025
com.example.standardtemplate.app-main-5\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\application_icon.png
com.example.standardtemplate.app-main-5\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\dialog_background.xml
com.example.standardtemplate.app-main-5\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_back.xml
com.example.standardtemplate.app-main-5\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_close.xml
com.example.standardtemplate.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_background.xml
com.example.standardtemplate.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_foreground.xml
com.example.standardtemplate.app-main-5\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_noti_background.xml
com.example.standardtemplate.app-main-5\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\input_field_background.xml
com.example.standardtemplate.app-main-5\:/drawable/radio_button_red.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\radio_button_red.xml
com.example.standardtemplate.app-main-5\:/drawable/radio_button_unchecked.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\radio_button_unchecked.xml
com.example.standardtemplate.app-main-5\:/drawable/rounded_background_light.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\rounded_background_light.xml
com.example.standardtemplate.app-main-5\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_accepted_ticket_details.xml
com.example.standardtemplate.app-main-5\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_accepted_ticket_list.xml
com.example.standardtemplate.app-main-5\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_login.xml
com.example.standardtemplate.app-main-5\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_new_ticket_details.xml
com.example.standardtemplate.app-main-5\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_new_ticket_list.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_action_button.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_action_button.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_audio_capture.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_audio_capture.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_barcode_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_barcode_applet.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_bnf_grammar.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_bnf_grammar.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_camera_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_camera_applet.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_document_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_document_applet.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_help_menu.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_help_menu.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_keyboard_dictation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_keyboard_dictation.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_main.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_main.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_microphone_release.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_microphone_release.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_movie_applet.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_movie_applet.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_speech_recognizer.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_speech_recognizer.xml
com.example.standardtemplate.app-main-5\:/layout/activity_realwear_text_to_speech.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_realwear_text_to_speech.xml
com.example.standardtemplate.app-main-5\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_register.xml
com.example.standardtemplate.app-main-5\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_confirmation.xml
com.example.standardtemplate.app-main-5\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_create.xml
com.example.standardtemplate.app-main-5\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_message.xml
com.example.standardtemplate.app-main-5\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_setting.xml
com.example.standardtemplate.app-main-5\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_accepted_ticket_list.xml
com.example.standardtemplate.app-main-5\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\recycle_view_new_ticket_list.xml
com.example.standardtemplate.app-main-5\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\spinner_item.xml
com.example.standardtemplate.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.standardtemplate.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.standardtemplate.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-5\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-5\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-5\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-5\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\backup_rules.xml
com.example.standardtemplate.app-main-5\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\data_extraction_rules.xml
com.example.standardtemplate.app-main-5\:/xml/file_provider_paths.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\file_provider_paths.xml
com.example.standardtemplate.app-main-5\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\locales_config.xml

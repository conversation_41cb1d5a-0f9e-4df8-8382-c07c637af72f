package com.example.standardtemplate.Activities.AcceptedTicket

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Models.TicketDone
import com.example.standardtemplate.R
import retrofit2.Call
import retrofit2.Response

class AcceptedTicketDetailsActivity : AppCompatActivity() {
    private lateinit var sharedPref: SharedPref
    private lateinit var btnReturn: Button
    private lateinit var btnDone: Button

    private val token = UserInfoManager.token.toString()
    private val username = UserInfoManager.username.toString()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_accepted_ticket_details)

        enableEdgeToEdge()
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

//        sharedPref = SharedPref(this@AcceptedTicketDetailsActivity)

        val ticketId = intent.getIntExtra("id", 0)
        val machineId = intent.getStringExtra("machineId") ?: "N/A"
        val errorMsg = intent.getStringExtra("errorMsg") ?: "N/A"
        val machineStatus = intent.getStringExtra("machineStatus") ?: "N/A"
        val createdAt = intent.getStringExtra("createdAt") ?: "N/A"
        val attendBy = username
        val status = intent.getStringExtra("status") ?: "N/A"
        val remedy = intent.getStringExtra("remedy") ?: ""
        val completeAt = intent.getStringExtra("completeAt") ?: ""
        val downtime = intent.getStringExtra("downtime")?:""

        val tvTicketId = findViewById<TextView>(R.id.tvTicketId)
        val tvMachineId = findViewById<TextView>(R.id.tvMachineId)
        val tvErrorMsg = findViewById<TextView>(R.id.tvErrorMsg)
        val tvMachineStatus = findViewById<TextView>(R.id.tvMachineStatus)
        val tvCreatedAt = findViewById<TextView>(R.id.tvCreatedAt)
        val tvAttendBy = findViewById<TextView>(R.id.tvAttendBy)
        val tvStatus = findViewById<TextView>(R.id.tvStatus)
        val edtRemedy = findViewById<EditText>(R.id.edtRemedy)
        val tvCompleteAt = findViewById<TextView>(R.id.tvCompleteAt)
        val tvDownTime=findViewById<TextView>(R.id.txtMachineDowntime)

        tvTicketId.text = "Ticket ID: $ticketId"
        tvMachineId.text = "Machine ID: $machineId"
        tvErrorMsg.text = "Error Message: $errorMsg"
        tvMachineStatus.text = "Machine Status: $machineStatus"
        tvCreatedAt.text = "Created At: $createdAt"
        tvAttendBy.text = "Attended By: $attendBy"
        tvStatus.text = "Status: $status"
        tvDownTime.text="Machine Downtime: $downtime"

        if (remedy.isEmpty() && completeAt.isEmpty()) {
            //edtRemedy.setHint("Remedy: ")
            edtRemedy.isFocusable = true
        } else {
            edtRemedy.setText(remedy)
        }

        btnReturn = findViewById(R.id.btnBack)
        btnReturn.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
            finish()
        }

        btnDone = findViewById(R.id.btnDone)
        btnDone.setOnClickListener {
            val remedy = edtRemedy.text
            if (remedy != null && remedy.toString() != ""){
                solvedTicket(token,ticketId, remedy.toString())
            }else{
                Log.w("Remedy", "Remedy is empty")
                Toast.makeText(this, "Remedy Cannot Be Empty", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun solvedTicket(token: String, ticketId: Int, remedy: String) {
        val apiService = ApiClient.getAuthenticatedClient(this,token).create(ApiInterface::class.java)
//        val notificationHelper = NotificationHelper(applicationContext)

        val ticketDone = TicketDone(
            ticketId = ticketId.toInt(),
            remedy = remedy
        )

        apiService.solvedTicket(ticketDone).enqueue(object: retrofit2.Callback<Void>{
            override fun onResponse(call: Call<Void>, response: Response<Void>){
                if (response.isSuccessful) {
//                    notificationHelper.sendNotificationWithTask("Completed Ticket", "You Have Completed 1 Ticket!")
                    Toast.makeText(this@AcceptedTicketDetailsActivity, "You have completed 1 ticket!", Toast.LENGTH_LONG).show()
                    onBackPressedDispatcher.onBackPressed()
                    finish()
                }else{
                    Log.d("Done Ticket Failed", "Ticket Not Yet Done")
//                    Toast.makeText(this@AcceptedTicketDetailsActivity, "Failed to Submit request! Please try again.", Toast.LENGTH_LONG).show()
                    Toast.makeText(applicationContext, "${getString(R.string.DT_001)}: ${getString(R.string.errorCode_DT_001)}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<Void>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()            }

        })
    }


}

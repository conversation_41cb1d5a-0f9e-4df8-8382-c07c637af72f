package com.example.standardtemplate.Models

import com.google.gson.annotations.SerializedName

data class RegisterResponse(
    @SerializedName("Id") val id: Int,
    @SerializedName("Username") val username: String,
    @SerializedName("Fullname") val fullname: String,
    @SerializedName("Password") val password: String,
    @SerializedName("CreateAt") val createdAt: String,
    @SerializedName("TicketAttendByNavigations") val ticketAttendByNavigations: List<Any>,
    @SerializedName("TicketCreatedByNavigations") val ticketCreatedByNavigations: List<Any>

)

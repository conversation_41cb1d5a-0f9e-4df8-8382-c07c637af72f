package com.example.standardtemplate.Activities.RealWear

import android.app.Activity
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import androidx.activity.OnBackPressedCallback
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.content.pm.PackageManager
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.util.UUID

/**
 * RealWear Camera Applet Activity
 * 
 * This activity demonstrates how to use the camera to take pictures and record videos 
 * on a RealWear HMT device. It provides multiple capture modes including bitmap photos,
 * file provider photos, basic video, and file provider video recording.
 * 
 * RealWear Development Software, Source Code and Object Code.
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * Features:
 * - Bitmap photo capture (returns small preview image)
 * - File provider photo capture (saves full resolution to gallery)
 * - Basic video recording
 * - File provider video recording (saves to gallery)
 * - Video playback after recording
 */
class CameraAppletActivity : BaseActivity() {
    
    companion object {
        private const val TAG = "CameraAppletActivity"

        // Request code for playing back videos
        private const val FILE_PLAYBACK_REQUEST_CODE = 5

        // Request codes for identifying camera events
        private const val BITMAP_PHOTO_REQUEST_CODE = 1
        private const val FILE_PROVIDER_PHOTO_REQUEST_CODE = 2
        private const val BASIC_VIDEO_REQUEST_CODE = 3
        private const val FILE_PROVIDER_VIDEO_REQUEST_CODE = 4

        // Permission request code
        private const val CAMERA_PERMISSION_REQUEST_CODE = 100
        
        // Locations for storing content provided images and videos
        private val DEFAULT_IMAGE_LOCATION = "${Environment.DIRECTORY_DCIM}/Camera"
        private val DEFAULT_VIDEO_LOCATION = "${Environment.DIRECTORY_MOVIES}/Camera"
        
        // Identifier for the image returned by the camera
        private const val EXTRA_RESULT = "data"
        
        // Intent flag for granting read URI permission
        private const val FLAG_GRANT_READ_URI_PERMISSION = Intent.FLAG_GRANT_READ_URI_PERMISSION
    }
    
    // UI Components
    private lateinit var mImageView: ImageView
    private lateinit var btnBitmapPhoto: Button
    private lateinit var btnFileProviderPhoto: Button
    private lateinit var btnBasicVideo: Button
    private lateinit var btnFileProviderVideo: Button
    private lateinit var btnBack: Button
    
    /**
     * Called when the activity is created
     * 
     * @param savedInstanceState See Android documentation
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set fullscreen mode for optimal RealWear device display
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_camera_applet)

        // Check and request camera permission
        checkCameraPermission()

        // Initialize UI components
        initializeViews()

        // Setup click listeners
        setupClickListeners()

        // Setup back press handler
        setupBackPressedHandler()
    }
    
    /**
     * Check and request camera permission if needed
     */
    private fun checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {

            Log.d(TAG, "Camera permission not granted, requesting permission")

            // Request camera permission
            ActivityCompat.requestPermissions(
                this,
                arrayOf(android.Manifest.permission.CAMERA),
                CAMERA_PERMISSION_REQUEST_CODE
            )
        } else {
            Log.d(TAG, "Camera permission already granted")
        }
    }

    /**
     * Handle permission request results
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Camera permission granted by user")
                    Toast.makeText(this, "相机权限已授予", Toast.LENGTH_SHORT).show()
                } else {
                    Log.w(TAG, "Camera permission denied by user")
                    Toast.makeText(this, "需要相机权限才能使用相机功能", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Initialize all UI components
     */
    private fun initializeViews() {
        mImageView = findViewById(R.id.camera_image_view)
        btnBitmapPhoto = findViewById(R.id.btnBitmapPhoto)
        btnFileProviderPhoto = findViewById(R.id.btnFileProviderPhoto)
        btnBasicVideo = findViewById(R.id.btnBasicVideo)
        btnFileProviderVideo = findViewById(R.id.btnFileProviderVideo)
        btnBack = findViewById(R.id.btnBack)
    }
    
    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        btnBitmapPhoto.setOnClickListener { onLaunchBitmapPhoto() }
        btnFileProviderPhoto.setOnClickListener { onLaunchFileProviderPhoto() }
        btnBasicVideo.setOnClickListener { onLaunchBasicVideo() }
        btnFileProviderVideo.setOnClickListener { onLaunchFileProviderVideo() }
        btnBack.setOnClickListener { finish() }
    }
    
    /**
     * Setup back press handler to maintain consistent navigation behavior
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
            }
        })
    }

    /**
     * Show error message to user
     */
    private fun showErrorMessage(message: String) {
        Log.e(TAG, message)
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    /**
     * Launch bitmap photo capture. Returns a small preview image as bitmap.
     * This method captures a photo and returns it as a bitmap object.
     */
    private fun onLaunchBitmapPhoto() {
        Log.d(TAG, "Attempting to launch bitmap photo capture")

        // Check camera permission first
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Camera permission not granted, requesting permission")
            Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_SHORT).show()
            checkCameraPermission()
            return
        }

        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)

        // Check if there's an app that can handle this intent
        if (intent.resolveActivity(packageManager) != null) {
            Log.d(TAG, "Camera app found, launching bitmap photo capture")
            try {
                startActivityForResult(intent, BITMAP_PHOTO_REQUEST_CODE)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start bitmap photo capture", e)
                showErrorMessage("无法启动相机: ${e.message}")
            }
        } else {
            Log.e(TAG, "No camera app found to handle bitmap photo capture")
            showErrorMessage("设备上没有找到相机应用")
        }
    }
    
    /**
     * Launch file provider photo capture. Saves full resolution image to gallery.
     * This method captures a photo and saves it directly to the device storage.
     */
    private fun onLaunchFileProviderPhoto() {
        Log.d(TAG, "Attempting to launch file provider photo capture")

        // Check camera permission first
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Camera permission not granted, requesting permission")
            Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_SHORT).show()
            checkCameraPermission()
            return
        }

        try {
            val fileName = "devexamples-${UUID.randomUUID()}.jpg"

            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                put(MediaStore.MediaColumns.MIME_TYPE, "image/jpg")
                put(MediaStore.MediaColumns.RELATIVE_PATH, DEFAULT_IMAGE_LOCATION)
            }

            val contentUri = baseContext.contentResolver.insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )

            if (contentUri == null) {
                showErrorMessage("无法创建图片文件，请检查存储权限")
                return
            }

            val captureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE).apply {
                addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
                putExtra(MediaStore.EXTRA_OUTPUT, contentUri)
            }

            if (captureIntent.resolveActivity(packageManager) != null) {
                Log.d(TAG, "Camera app found, launching file provider photo capture")
                startActivityForResult(captureIntent, FILE_PROVIDER_PHOTO_REQUEST_CODE)
            } else {
                Log.e(TAG, "No camera app found to handle file provider photo capture")
                showErrorMessage("设备上没有找到相机应用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start file provider photo capture", e)
            showErrorMessage("启动相机失败: ${e.message}")
        }
    }
    
    /**
     * Launch basic video recording. Returns video data through intent.
     * This method starts video recording and returns the recorded video.
     */
    private fun onLaunchBasicVideo() {
        Log.d(TAG, "Attempting to launch basic video recording")

        // Check camera permission first
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Camera permission not granted, requesting permission")
            Toast.makeText(this, "需要相机权限才能录制视频", Toast.LENGTH_SHORT).show()
            checkCameraPermission()
            return
        }

        val intent = Intent(MediaStore.ACTION_VIDEO_CAPTURE)

        if (intent.resolveActivity(packageManager) != null) {
            Log.d(TAG, "Video recording app found, launching basic video capture")
            try {
                startActivityForResult(intent, BASIC_VIDEO_REQUEST_CODE)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start basic video recording", e)
                showErrorMessage("无法启动视频录制: ${e.message}")
            }
        } else {
            Log.e(TAG, "No video recording app found")
            showErrorMessage("设备上没有找到视频录制应用")
        }
    }
    
    /**
     * Launch file provider video recording. Saves video to gallery.
     * This method records video and saves it directly to device storage.
     */
    private fun onLaunchFileProviderVideo() {
        Log.d(TAG, "Attempting to launch file provider video recording")

        // Check camera permission first
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Camera permission not granted, requesting permission")
            Toast.makeText(this, "需要相机权限才能录制视频", Toast.LENGTH_SHORT).show()
            checkCameraPermission()
            return
        }

        try {
            val fileName = "devexamples-${UUID.randomUUID()}.mp4"

            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
                put(MediaStore.MediaColumns.MIME_TYPE, "video/mp4")
                put(MediaStore.MediaColumns.RELATIVE_PATH, DEFAULT_VIDEO_LOCATION)
            }

            val contentUri = baseContext.contentResolver.insert(
                MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )

            if (contentUri == null) {
                showErrorMessage("无法创建视频文件，请检查存储权限")
                return
            }

            val captureIntent = Intent(MediaStore.ACTION_VIDEO_CAPTURE).apply {
                addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
                putExtra(MediaStore.EXTRA_OUTPUT, contentUri)
            }

            if (captureIntent.resolveActivity(packageManager) != null) {
                Log.d(TAG, "Video recording app found, launching file provider video capture")
                startActivityForResult(captureIntent, FILE_PROVIDER_VIDEO_REQUEST_CODE)
            } else {
                Log.e(TAG, "No video recording app found")
                showErrorMessage("设备上没有找到视频录制应用")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start file provider video recording", e)
            showErrorMessage("启动视频录制失败: ${e.message}")
        }
    }
    
    /**
     * Handle results from external activities. Receives image/video data from camera.
     * 
     * @param requestCode Identifies which camera operation was performed
     * @param resultCode Result status from the camera activity
     * @param data Intent containing the captured media data
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (resultCode == Activity.RESULT_OK && data != null) {
            when (requestCode) {
                BITMAP_PHOTO_REQUEST_CODE -> {
                    // Handle bitmap photo result - display small preview image
                    val photo = data.extras?.getParcelable<Bitmap>(EXTRA_RESULT)
                    photo?.let { mImageView.setImageBitmap(it) }
                }
                
                FILE_PROVIDER_PHOTO_REQUEST_CODE -> {
                    // Handle file provider photo result - display from URI
                    val photoUri = data.data
                    photoUri?.let { mImageView.setImageURI(it) }
                }
                
                BASIC_VIDEO_REQUEST_CODE, FILE_PROVIDER_VIDEO_REQUEST_CODE -> {
                    // Handle video recording result - launch video player
                    val videoUri = data.data
                    videoUri?.let { playVideo(it) }
                }
                
                else -> {
                    Log.e(TAG, "Unknown request code: $requestCode")
                }
            }
        } else {
            Log.w(TAG, "Camera operation cancelled or failed. ResultCode: $resultCode")
        }
    }
    
    /**
     * Launch video player to play recorded video
     * 
     * @param videoUri URI of the video to play
     */
    private fun playVideo(videoUri: Uri) {
        val basicVideoPlayIntent = Intent().apply {
            action = Intent.ACTION_VIEW
            addFlags(FLAG_GRANT_READ_URI_PERMISSION)
            setDataAndType(videoUri, "video/*")
        }
        
        try {
            startActivityForResult(basicVideoPlayIntent, FILE_PLAYBACK_REQUEST_CODE)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch video player", e)
            // Could show a toast message to user here if needed
        }
    }
}

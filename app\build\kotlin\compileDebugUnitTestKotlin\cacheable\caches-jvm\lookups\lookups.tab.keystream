  ExampleUnitTest com.example.standardtemplate  assertEquals com.example.standardtemplate  Test ,com.example.standardtemplate.ExampleUnitTest  assertEquals ,com.example.standardtemplate.ExampleUnitTest  getASSERTEquals ,com.example.standardtemplate.ExampleUnitTest  getAssertEquals ,com.example.standardtemplate.ExampleUnitTest  assertEquals 	java.lang  Int kotlin  assertEquals kotlin  assertEquals kotlin.annotation  assertEquals kotlin.collections  assertEquals kotlin.comparisons  assertEquals 	kotlin.io  assertEquals 
kotlin.jvm  assertEquals 
kotlin.ranges  assertEquals kotlin.sequences  assertEquals kotlin.text  Assert 	org.junit  Test 	org.junit  assertEquals org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
package com.example.standardtemplate.Activities.RealWear

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.activity.OnBackPressedCallback
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.Dialogs.ConfirmationDialog.showConfirmDialog
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R

/**
 * RealWear功能主界面Activity
 * 
 * 此Activity展示所有可用的RealWear功能选项，用户可以选择不同的功能进行测试
 * 包含以下RealWear功能演示：
 * - Action Button：操作按钮功能
 * - Camera Applet：相机功能（待实现）
 * - Document Viewer：文档查看器（待实现）
 * - Speech Recognition：语音识别（待实现）
 * 
 * 功能特点：
 * - 遵循标准模板的设计规范
 * - 支持多语言（中英文）
 * - 统一的导航和退出逻辑
 * - 与现有票务系统集成
 */
class RealWearMainActivity : BaseActivity() {
    
    // UI组件
    private lateinit var btnActionButton: Button
    private lateinit var btnCameraApplet: Button
    private lateinit var btnDocumentViewer: Button
    private lateinit var btnMovieApplet: Button
    private lateinit var btnBarcodeApplet: Button
    private lateinit var btnKeyboardDictation: Button
    private lateinit var btnSpeechRecognition: Button
    private lateinit var btnTextToSpeech: Button
    private lateinit var btnMicrophoneRelease: Button
    private lateinit var btnAudioCapture: Button
    private lateinit var btnHelpMenu: Button
    private lateinit var btnBNFGrammar: Button
    private lateinit var btnBackToMain: Button
    
    /**
     * Activity创建时调用
     * 
     * @param savedInstanceState 保存的实例状态，参见Android文档
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_realwear_main)
        
        // 设置系统栏适配
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
        
        // 初始化界面组件
        initializeViews()
        
        // 设置点击事件
        setupClickListeners()
        
        // 设置返回按键处理
        setupBackPressedHandler()
    }
    
    /**
     * 初始化界面组件
     */
    private fun initializeViews() {
        btnActionButton = findViewById(R.id.btnActionButton)
        btnCameraApplet = findViewById(R.id.btnCameraApplet)
        btnDocumentViewer = findViewById(R.id.btnDocumentViewer)
        btnMovieApplet = findViewById(R.id.btnMovieApplet)
        btnBarcodeApplet = findViewById(R.id.btnBarcodeApplet)
        btnKeyboardDictation = findViewById(R.id.btnKeyboardDictation)
        btnSpeechRecognition = findViewById(R.id.btnSpeechRecognition)
        btnTextToSpeech = findViewById(R.id.btnTextToSpeech)
        btnMicrophoneRelease = findViewById(R.id.btnMicrophoneRelease)
        btnAudioCapture = findViewById(R.id.btnAudioCapture)
        btnHelpMenu = findViewById(R.id.btnHelpMenu)
        btnBNFGrammar = findViewById(R.id.btnBNFGrammar)
        btnBackToMain = findViewById(R.id.btnBackToMain)
    }
    
    /**
     * 设置按钮点击事件
     */
    private fun setupClickListeners() {
        // Action Button功能入口
        btnActionButton.setOnClickListener {
            val intent = Intent(this, ActionButtonActivity::class.java)
            startActivity(intent)
        }
        
        // Camera Applet功能入口
        btnCameraApplet.setOnClickListener {
            val intent = Intent(this, CameraAppletActivity::class.java)
            startActivity(intent)
        }
        
        // Document Viewer功能入口
        btnDocumentViewer.setOnClickListener {
            val intent = Intent(this, DocumentAppletActivity::class.java)
            startActivity(intent)
        }
        
        // Movie Applet功能入口
        btnMovieApplet.setOnClickListener {
            val intent = Intent(this, MovieAppletActivity::class.java)
            startActivity(intent)
        }
        
        // Barcode Applet功能入口
        btnBarcodeApplet.setOnClickListener {
            val intent = Intent(this, BarcodeAppletActivity::class.java)
            startActivity(intent)
        }
        
        // Keyboard & Dictation功能入口
        btnKeyboardDictation.setOnClickListener {
            val intent = Intent(this, KeyboardDictationActivity::class.java)
            startActivity(intent)
        }
        
        // Speech Recognition功能入口（已实现）
        btnSpeechRecognition.setOnClickListener {
            val intent = Intent(this, SpeechRecognizerActivity::class.java)
            startActivity(intent)
        }
        
        // Text to Speech功能入口（已实现）
        btnTextToSpeech.setOnClickListener {
            val intent = Intent(this, TextToSpeechActivity::class.java)
            startActivity(intent)
        }
        
        // Microphone Release功能入口（已实现）
        btnMicrophoneRelease.setOnClickListener {
            val intent = Intent(this, MicrophoneReleaseActivity::class.java)
            startActivity(intent)
        }
        
        // Audio Capture功能入口（已实现）
        btnAudioCapture.setOnClickListener {
            val intent = Intent(this, AudioCaptureActivity::class.java)
            startActivity(intent)
        }
        
        // Help Menu功能入口（已实现）
        btnHelpMenu.setOnClickListener {
            val intent = Intent(this, HelpMenuActivity::class.java)
            startActivity(intent)
        }
        
        // BNF Grammar功能入口（已实现）
        btnBNFGrammar.setOnClickListener {
            val intent = Intent(this, BNFGrammarActivity::class.java)
            startActivity(intent)
        }
        
        // 返回主界面按钮
        btnBackToMain.setOnClickListener {
            showConfirmDialog(
                this@RealWearMainActivity,
                getString(R.string.confirm),
                getString(R.string.realwear_back_to_main_msg)
            ) {
                val intent = Intent(this, NewTicketListActivity::class.java)
                startActivity(intent)
                finish()
            }
        }
    }
    
    /**
     * 设置返回键处理逻辑
     * 与标准模板保持一致的用户体验
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                showConfirmDialog(
                    this@RealWearMainActivity,
                    getString(R.string.confirm),
                    getString(R.string.realwear_back_to_main_msg)
                ) {
                    val intent = Intent(this@RealWearMainActivity, NewTicketListActivity::class.java)
                    startActivity(intent)
                    finish()
                }
            }
        })
    }
    
    /**
     * 显示临时消息提示
     * 用于尚未实现的功能
     * 
     * @param message 要显示的消息
     */
    private fun showTemporaryMessage(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }
}


package com.example.standardtemplate.Libraries

import android.content.Context
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import okhttp3.Dns
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object ApiClient {
    private var authenticatedRetrofit: Retrofit? = null
    private var unauthenticatedRetrofit: Retrofit? = null

    private lateinit var sharedPref : SharedPref

    //used to clear the baseurl that insert by user previously
    fun clearVals(){
        authenticatedRetrofit = null
        unauthenticatedRetrofit = null
    }

    fun getAuthenticatedClient(context: Context, token: String): Retrofit {
        sharedPref = SharedPref(context)
        if (authenticatedRetrofit == null) {
            //val baseUrl = getBaseUrl(context) // Always get the latest BASE_URL
            val baseUrl = sharedPref.getBaseUrl(context)

            val authInterceptor = Interceptor { chain ->
                val newRequest = chain.request().newBuilder()
                    .addHeader("Authorization", "Bearer $token")
                    .addHeader("User-Agent", "Mozilla/5.0")
                    .build()
                chain.proceed(newRequest)
            }

            val loggingInterceptor = HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            }

            val client = OkHttpClient.Builder()
                .dns(Dns.SYSTEM)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectTimeout(30, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(authInterceptor)
                .build()

            val gson: Gson = GsonBuilder()
                .setLenient()
                .create()

            authenticatedRetrofit = Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .client(client)
                .build()
        }
        return authenticatedRetrofit!!
    }

    fun getUnauthenticatedClient(context: Context): Retrofit {
        sharedPref = SharedPref(context)
        if (unauthenticatedRetrofit == null) {
            //val baseUrl = getBaseUrl(context) // Always get the latest BASE_URL
            val baseUrl = sharedPref.getBaseUrl(context)

            val noAuthInterceptor = Interceptor { chain ->
                val newRequest = chain.request().newBuilder()
                    .addHeader("User-Agent", "Mozilla/5.0")
                    .build()
                chain.proceed(newRequest)
            }

            val loggingInterceptor = HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.NONE
            }

            val client = OkHttpClient.Builder()
                .dns(Dns.SYSTEM)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectTimeout(30, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .addInterceptor(noAuthInterceptor)
                .build()

            val gson = GsonBuilder()
                .setLenient()
                .create()

            unauthenticatedRetrofit = Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .client(client)
                .build()
        }
        return unauthenticatedRetrofit!!
    }
}
{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeReleaseResources-44:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\838052c77a74c243fbc236b2a83bb29c\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,6494,6562,6632,6722,6792,6852,6939,7005,7070,7131,7195,7256,7310,7411,7472,7532,7586,7656,7767,7854,7931,8018,8100,8181,8324,8403,8485,8617,8709,8787,8841,8894,8960,9030,9108,9179,9259,9331,9409,9478,9547,9645,9727,9815,9908,10002,10076,10145,10240,10292,10375,10443,10528,10616,10678,10742,10805,10875,10975,11071,11168,11261,11319,11376,11535,11617,11692", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,6557,6627,6717,6787,6847,6934,7000,7065,7126,7190,7251,7305,7406,7467,7527,7581,7651,7762,7849,7926,8013,8095,8176,8319,8398,8480,8612,8704,8782,8836,8889,8955,9025,9103,9174,9254,9326,9404,9473,9542,9640,9722,9810,9903,9997,10071,10140,10235,10287,10370,10438,10523,10611,10673,10737,10800,10870,10970,11066,11163,11256,11314,11371,11448,11612,11687,11763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6b3ff4ec66daf8ab6ce732fa9c600777\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,11453", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,11530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85d1553e1b933cd2e13c79366a035914\\transformed\\play-services-base-18.0.1\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4476,4623,4746,4853,4989,5113,5232,5469,5613,5718,5865,5987,6127,6278,6342,6410", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4471,4618,4741,4848,4984,5108,5227,5335,5608,5713,5860,5982,6122,6273,6337,6405,6489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6da3c2cc95131e6e0714e67e2bba3b1b\\transformed\\core-1.15.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,11768", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,11864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b5fd8a1debeeacbb6bf1c31739d4dca\\transformed\\play-services-basement-18.1.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5340", "endColumns": "128", "endOffsets": "5464"}}]}]}
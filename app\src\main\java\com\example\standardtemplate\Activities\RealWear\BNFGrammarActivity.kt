/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity that shows how to register BNF grammar on a RealWear device.
 * BNF (Backus-Naur Form) allows defining complex voice command patterns.
 */
class BNFGrammarActivity : BaseActivity() {

    companion object {
        private const val TAG = "BNFGrammarActivity"
        
        // The action that WearHF will use for broadcasting when a voice command is spoken
        private const val ACTION_SPEECH_EVENT = "com.realwear.wearhf.intent.action.SPEECH_EVENT"
        private const val EXTRA_COMMAND = "command"
    }

    private lateinit var mBnfDescriptionView: TextView
    private lateinit var mStatusView: TextView
    private lateinit var mCommandHistoryView: TextView
    private lateinit var btnSetTimeGrammar: Button
    private lateinit var btnSetNumberGrammar: Button
    private lateinit var btnClearGrammar: Button
    private lateinit var btnClearHistory: Button
    private lateinit var btnBack: Button
    
    private val commandHistory = mutableListOf<String>()
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    private var currentGrammarType = "None"

    // BNF grammar definitions
    private val timeGrammar = "#BNF+EM V2.0;" +
            "!grammar Commands;\n" +
            "!start <Commands>;\n" +
            "<Commands>:<global_commands>|<Hour> !optional(<Minute>);\n" +
            "<Minute>:1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27|28|29|30|31|32|33|34|35|36|37|38|39|40|41|42|43|44|45|46|47|48|49|50|51|52|53|54|55|56|57|58|59;\n" +
            "<Hour>:1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24;"

    private val numberGrammar = "#BNF+EM V2.0;" +
            "!grammar Commands;\n" +
            "!start <Commands>;\n" +
            "<Commands>:<global_commands>|<Action> <Number>;\n" +
            "<Action>:Select|Choose|Pick|Go to|Navigate to;\n" +
            "<Number>:Zero|One|Two|Three|Four|Five|Six|Seven|Eight|Nine|Ten|Eleven|Twelve|Thirteen|Fourteen|Fifteen|Sixteen|Seventeen|Eighteen|Nineteen|Twenty;"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_bnf_grammar)

        initializeViews()
        setupClickListeners()
        updateStatus(getString(R.string.realwear_bnf_ready))
        
        Log.d(TAG, "BNF Grammar Activity initialized")
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mBnfDescriptionView = findViewById(R.id.bnfDescriptionView)
        mStatusView = findViewById(R.id.statusView)
        mCommandHistoryView = findViewById(R.id.commandHistoryView)
        btnSetTimeGrammar = findViewById(R.id.btnSetTimeGrammar)
        btnSetNumberGrammar = findViewById(R.id.btnSetNumberGrammar)
        btnClearGrammar = findViewById(R.id.btnClearGrammar)
        btnClearHistory = findViewById(R.id.btnClearHistory)
        btnBack = findViewById(R.id.btnBack)
        
        // Initialize views
        updateCommandHistory()
    }

    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        btnSetTimeGrammar.setOnClickListener {
            setTimeGrammar()
        }
        
        btnSetNumberGrammar.setOnClickListener {
            setNumberGrammar()
        }
        
        btnClearGrammar.setOnClickListener {
            clearGrammar()
        }
        
        btnClearHistory.setOnClickListener {
            clearCommandHistory()
        }
        
        btnBack.setOnClickListener {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        
        // Register receiver for speech events
        val filter = IntentFilter(ACTION_SPEECH_EVENT)
        registerReceiver(asrBroadcastReceiver, filter)
        
        updateStatus(getString(R.string.realwear_bnf_listening))
        Log.d(TAG, "Speech event receiver registered")
    }

    override fun onPause() {
        super.onPause()
        
        // Unregister receiver to avoid memory leaks
        try {
            unregisterReceiver(asrBroadcastReceiver)
            Log.d(TAG, "Speech event receiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering receiver", e)
        }
    }

    /**
     * Set time-based BNF grammar (Hour + optional Minute)
     */
    private fun setTimeGrammar() {
        try {
            Log.d(TAG, "Setting time grammar")
            
            // Set the BNF grammar to the description view
            // The "hf_override:" prefix tells RealWear to use this custom grammar
            mBnfDescriptionView.contentDescription = "hf_override:$timeGrammar"
            
            currentGrammarType = "Time"
            updateStatus(getString(R.string.realwear_bnf_time_grammar_set))
            Toast.makeText(this, R.string.realwear_bnf_time_grammar_set, Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Time grammar set successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set time grammar", e)
            updateStatus(getString(R.string.realwear_bnf_grammar_set_failed))
            Toast.makeText(this, R.string.realwear_bnf_grammar_set_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Set number-based BNF grammar (Action + Number)
     */
    private fun setNumberGrammar() {
        try {
            Log.d(TAG, "Setting number grammar")
            
            // Set the BNF grammar to the description view
            mBnfDescriptionView.contentDescription = "hf_override:$numberGrammar"
            
            currentGrammarType = "Number"
            updateStatus(getString(R.string.realwear_bnf_number_grammar_set))
            Toast.makeText(this, R.string.realwear_bnf_number_grammar_set, Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Number grammar set successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set number grammar", e)
            updateStatus(getString(R.string.realwear_bnf_grammar_set_failed))
            Toast.makeText(this, R.string.realwear_bnf_grammar_set_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Clear the current BNF grammar
     */
    private fun clearGrammar() {
        try {
            Log.d(TAG, "Clearing grammar")
            
            // Clear the content description to remove custom grammar
            mBnfDescriptionView.contentDescription = ""
            
            currentGrammarType = "None"
            updateStatus(getString(R.string.realwear_bnf_grammar_cleared))
            Toast.makeText(this, R.string.realwear_bnf_grammar_cleared, Toast.LENGTH_SHORT).show()
            
            Log.d(TAG, "Grammar cleared successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear grammar", e)
            updateStatus(getString(R.string.realwear_bnf_grammar_clear_failed))
            Toast.makeText(this, R.string.realwear_bnf_grammar_clear_failed, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Clear the command history
     */
    private fun clearCommandHistory() {
        commandHistory.clear()
        updateCommandHistory()
        updateStatus(getString(R.string.realwear_bnf_history_cleared))
        Toast.makeText(this, R.string.realwear_bnf_history_cleared, Toast.LENGTH_SHORT).show()
        
        Log.d(TAG, "Command history cleared")
    }

    /**
     * Handle received voice commands
     */
    private fun handleVoiceCommand(command: String) {
        val timestamp = dateFormatter.format(Date())
        
        // Add to command history
        commandHistory.add("[$timestamp] $command")
        if (commandHistory.size > 20) {
            commandHistory.removeAt(0) // Keep only last 20 commands
        }
        
        updateCommandHistory()
        updateStatus(getString(R.string.realwear_bnf_command_received, command))
        
        // Show toast with the recognized command
        Toast.makeText(this, getString(R.string.realwear_bnf_command_toast, command), Toast.LENGTH_LONG).show()
        
        Log.d(TAG, "Voice command received: $command")
    }

    /**
     * Update the command history display
     */
    private fun updateCommandHistory() {
        if (commandHistory.isEmpty()) {
            mCommandHistoryView.text = getString(R.string.realwear_bnf_no_commands)
        } else {
            val historyText = commandHistory.takeLast(10).reversed().joinToString("\n")
            mCommandHistoryView.text = getString(R.string.realwear_bnf_command_history, historyText)
        }
    }

    /**
     * Update the status display
     */
    private fun updateStatus(status: String) {
        val fullStatus = getString(R.string.realwear_bnf_status_format, currentGrammarType, status)
        mStatusView.text = fullStatus
    }

    /**
     * Broadcast receiver for being notified when speech events occur
     */
    private val asrBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            try {
                val action = intent?.action
                if (action == ACTION_SPEECH_EVENT) {
                    val asrCommand = intent.getStringExtra(EXTRA_COMMAND)
                    if (!asrCommand.isNullOrEmpty()) {
                        handleVoiceCommand(asrCommand)
                    } else {
                        Log.w(TAG, "Received empty voice command")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing voice command", e)
            }
        }
    }
}


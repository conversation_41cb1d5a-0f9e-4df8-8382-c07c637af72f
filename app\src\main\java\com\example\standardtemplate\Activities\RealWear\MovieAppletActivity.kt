package com.example.standardtemplate.Activities.RealWear

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.WindowManager
import android.widget.Button
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.core.content.FileProvider
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Libraries.RealWear.DocumentUtils
import com.example.standardtemplate.R
import java.io.File
import java.io.IOException

/**
 * RealWear Movie Applet Activity
 * 
 * This activity demonstrates how to open videos in the movie viewer on a RealWear HMT device.
 * It shows how to launch external video players with various video file formats and provides
 * secure file access through FileProvider integration.
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * Features:
 * - Video file management from assets to external storage
 * - FileProvider integration for secure video file access
 * - External video player integration
 * - Multiple video format support (MP4, AVI, etc.)
 * - Sample video management and playback
 */
class MovieAppletActivity : BaseActivity() {
    
    companion object {
        // Sample video file configuration - can be customized for different video types
        private const val SAMPLE_VIDEO_FILE_NAME = "sample_video.mp4"
        private const val SAMPLE_FOLDER_NAME = "Movies"
        private const val VIDEO_MIME_TYPE = "video/mp4"
        
        // Alternative video files for demonstration
        private const val SAMPLE_AVI_FILE_NAME = "sample_video.avi"
        private const val AVI_MIME_TYPE = "video/avi"
    }
    
    // UI Components
    private lateinit var btnLaunchMp4Video: Button
    private lateinit var btnLaunchAviVideo: Button
    private lateinit var btnBack: Button
    
    // File references for sample videos
    private var mSampleMp4File: File? = null
    private var mSampleAviFile: File? = null
    
    /**
     * Called when the activity is created
     * 
     * @param savedInstanceState See Android documentation
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set fullscreen mode for optimal RealWear device display
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_movie_applet)
        
        // Initialize UI components
        initializeViews()
        
        // Setup sample videos
        initializeSampleVideos()
        
        // Setup click listeners
        setupClickListeners()
        
        // Setup back press handler
        setupBackPressedHandler()
    }
    
    /**
     * Initialize all UI components
     */
    private fun initializeViews() {
        btnLaunchMp4Video = findViewById(R.id.btnLaunchMp4Video)
        btnLaunchAviVideo = findViewById(R.id.btnLaunchAviVideo)
        btnBack = findViewById(R.id.btnBack)
    }
    
    /**
     * Initialize sample videos by copying them from assets to external storage
     * This ensures the videos are accessible by external video players
     */
    private fun initializeSampleVideos() {
        try {
            // Copy sample MP4 video from assets to external storage
            mSampleMp4File = DocumentUtils.copyFromAssetsToExternal(
                this, 
                SAMPLE_VIDEO_FILE_NAME, 
                SAMPLE_FOLDER_NAME
            )
            
            // Copy sample AVI video from assets to external storage (if available)
            try {
                mSampleAviFile = DocumentUtils.copyFromAssetsToExternal(
                    this, 
                    SAMPLE_AVI_FILE_NAME, 
                    SAMPLE_FOLDER_NAME
                )
            } catch (ex: IOException) {
                // AVI file might not exist in assets, which is okay
                android.util.Log.w("MovieAppletActivity", "AVI sample file not found in assets")
            }
            
        } catch (ex: IOException) {
            Toast.makeText(
                this, 
                getString(R.string.realwear_movie_copy_failed), 
                Toast.LENGTH_LONG
            ).show()
            
            // Log the error for debugging
            android.util.Log.e("MovieAppletActivity", "Failed to copy sample video files", ex)
        }
    }
    
    /**
     * Setup click listeners for all buttons
     */
    private fun setupClickListeners() {
        btnLaunchMp4Video.setOnClickListener { 
            launchVideo(mSampleMp4File, VIDEO_MIME_TYPE, "MP4") 
        }
        
        btnLaunchAviVideo.setOnClickListener { 
            launchVideo(mSampleAviFile, AVI_MIME_TYPE, "AVI") 
        }
        
        btnBack.setOnClickListener { finish() }
    }
    
    /**
     * Setup back press handler to maintain consistent navigation behavior
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finish()
            }
        })
    }
    
    /**
     * Launch video player for the specified video file
     * 
     * @param file The video file to play
     * @param mimeType The MIME type of the video
     * @param videoType Human-readable video type for error messages
     */
    private fun launchVideo(file: File?, mimeType: String, videoType: String) {
        // Validate that the file exists
        if (file == null || !file.exists()) {
            Toast.makeText(
                applicationContext,
                getString(R.string.realwear_movie_file_not_found, videoType),
                Toast.LENGTH_LONG
            ).show()
            return
        }
        
        try {
            // Create content URI using FileProvider for secure file access
            val contentUri = FileProvider.getUriForFile(
                applicationContext,
                "${applicationContext.packageName}.fileprovider",
                file
            )
            
            // Create intent to launch video player
            val viewIntent = Intent(Intent.ACTION_VIEW).apply {
                addCategory(Intent.CATEGORY_DEFAULT)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                setDataAndType(contentUri, mimeType)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // Launch the video player
            startActivity(viewIntent)
            
        } catch (ex: Exception) {
            // Handle cases where no suitable video player is available
            Toast.makeText(
                applicationContext,
                getString(R.string.realwear_movie_player_not_available, videoType),
                Toast.LENGTH_LONG
            ).show()
            
            android.util.Log.e("MovieAppletActivity", "Failed to launch video player", ex)
        }
    }
}


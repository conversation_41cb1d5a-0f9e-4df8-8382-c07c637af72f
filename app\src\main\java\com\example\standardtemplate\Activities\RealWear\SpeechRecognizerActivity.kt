/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.text.SimpleDateFormat
import java.util.*

/**
 * Activity that shows how to register voice commands with the ASR on a RealWear device.
 * This demo allows users to speak predefined voice commands and see the results displayed.
 */
class SpeechRecognizerActivity : BaseActivity() {

    companion object {
        private const val TAG = "SpeechRecognizerActivity"
        
        // The action that WearHF will use for broadcasting when a voice command is spoken
        private const val ACTION_SPEECH_EVENT = "com.realwear.wearhf.intent.action.SPEECH_EVENT"
        
        // Identifier for the voice command that was spoken
        private const val EXTRA_RESULT = "command"
    }

    private lateinit var mQuantityView: TextView
    private lateinit var mStatusView: TextView
    private lateinit var mResultsView: TextView
    private lateinit var btnClearResults: Button
    private lateinit var btnBack: Button
    
    private var commandCount = 0
    private val dateFormatter = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_speech_recognizer)

        initializeViews()
        setupVoiceCommands()
        updateStatus(getString(R.string.realwear_speech_recognizer_ready))
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mQuantityView = findViewById(R.id.quantityView)
        mStatusView = findViewById(R.id.statusView)
        mResultsView = findViewById(R.id.resultsView)
        btnClearResults = findViewById(R.id.btnClearResults)
        btnBack = findViewById(R.id.btnBack)

        // Set initial values
        mResultsView.text = getString(R.string.realwear_speech_recognizer_ready_message)
        
        // Setup click listeners
        btnClearResults.setOnClickListener {
            clearResults()
        }
        
        btnBack.setOnClickListener {
            finish()
        }
    }

    /**
     * Setup voice commands for this screen
     */
    private fun setupVoiceCommands() {
        try {
            // Set the voice commands for this screen
            // Adding voice commands to the TextView - they can be added to any view
            // The broadcast receiver will get the result when the voice command is spoken
            mQuantityView.contentDescription = "hf_add_commands:Quantity 1|Quantity 2|Quantity 3|Show Status|Clear Results|Go Back"
            
            Log.d(TAG, "Voice commands registered successfully")
            updateStatus(getString(R.string.realwear_speech_recognizer_commands_registered))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup voice commands", e)
            updateStatus(getString(R.string.realwear_speech_recognizer_setup_failed))
            Toast.makeText(this, R.string.realwear_speech_recognizer_setup_failed, Toast.LENGTH_LONG).show()
        }
    }

    override fun onResume() {
        super.onResume()
        
        try {
            // Register broadcast receiver for speech events
            registerReceiver(asrBroadcastReceiver, IntentFilter(ACTION_SPEECH_EVENT))
            Log.d(TAG, "Speech broadcast receiver registered")
            updateStatus(getString(R.string.realwear_speech_recognizer_listening))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register speech receiver", e)
            Toast.makeText(this, R.string.realwear_speech_recognizer_register_failed, Toast.LENGTH_LONG).show()
        }
    }

    override fun onPause() {
        super.onPause()
        
        try {
            // Unregister broadcast receiver to prevent memory leaks
            unregisterReceiver(asrBroadcastReceiver)
            Log.d(TAG, "Speech broadcast receiver unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to unregister speech receiver", e)
        }
    }

    /**
     * Broadcast receiver for being notified when voice commands are triggered
     */
    private val asrBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            try {
                val action = intent?.action
                if (action == ACTION_SPEECH_EVENT) {
                    val asrCommand = intent.getStringExtra(EXTRA_RESULT)
                    
                    if (!asrCommand.isNullOrEmpty()) {
                        handleVoiceCommand(asrCommand)
                    } else {
                        Log.w(TAG, "Received empty voice command")
                        updateStatus(getString(R.string.realwear_speech_recognizer_no_command))
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing voice command", e)
                updateStatus(getString(R.string.realwear_speech_recognizer_processing_error))
            }
        }
    }

    /**
     * Handle received voice commands
     */
    private fun handleVoiceCommand(command: String) {
        commandCount++
        val timestamp = dateFormatter.format(Date())
        
        Log.d(TAG, "Voice command received: $command")
        
        // Update the main display with the latest command
        mQuantityView.text = command
        
        // Add to results history
        val currentResults = mResultsView.text.toString()
        val newResult = "[$timestamp] Command #$commandCount: \"$command\""
        
        val updatedResults = if (currentResults == getString(R.string.realwear_speech_recognizer_ready_message)) {
            newResult
        } else {
            "$currentResults\n$newResult"
        }
        
        mResultsView.text = updatedResults
        
        // Update status
        updateStatus(getString(R.string.realwear_speech_recognizer_command_received, command))
        
        // Handle specific commands
        when (command.lowercase(Locale.getDefault())) {
            "show status" -> {
                Toast.makeText(this, getString(R.string.realwear_speech_recognizer_status_commands_received, commandCount), Toast.LENGTH_SHORT).show()
            }
            "clear results" -> {
                clearResults()
            }
            "go back" -> {
                finish()
            }
        }
    }

    /**
     * Clear all results and reset the display
     */
    private fun clearResults() {
        commandCount = 0
        mQuantityView.text = getString(R.string.realwear_speech_recognizer_no_command_yet)
        mResultsView.text = getString(R.string.realwear_speech_recognizer_ready_message)
        updateStatus(getString(R.string.realwear_speech_recognizer_results_cleared))
        
        Toast.makeText(this, R.string.realwear_speech_recognizer_results_cleared, Toast.LENGTH_SHORT).show()
        Log.d(TAG, "Results cleared")
    }

    /**
     * Update the status display
     */
    private fun updateStatus(status: String) {
        mStatusView.text = getString(R.string.realwear_speech_recognizer_status_label, status)
    }
}

package com.example.standardtemplate.Libraries.LanguageSetting

import android.app.Application
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Models.FcmTokenRequest
import com.google.firebase.messaging.FirebaseMessaging
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class StandardTemplate : Application() {

    private lateinit var sharedPref: SharedPref


    override fun onCreate() {
        super.onCreate()
        sharedPref = SharedPref(this)
        setLocale()
    }

    //used to set the locale of the template
    //apply in login page, work together with changeLanguage function
    fun setLocale() {
        // Retrieve the saved language preference
        val languageCode = sharedPref.getLanguage(this) ?: "en"
        Log.d("LocaleDebug", "Application locale set to: $languageCode")

        // Set the locale globally using AppCompatDelegate
        AppCompatDelegate.setApplicationLocales(LocaleListCompat.forLanguageTags(languageCode))
    }

}
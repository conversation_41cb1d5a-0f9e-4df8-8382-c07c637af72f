<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_description"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Text Input Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_input_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/textInput"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:hint="@string/realwear_tts_input_hint"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:gravity="top"
        android:inputType="textMultiLine"
        android:maxLines="3"
        android:layout_marginBottom="16dp" />

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnSpeakText"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_tts_speak_text"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnSpeakSample"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_tts_speak_sample"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnClearHistory"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_tts_clear_history"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnBack"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_tts_back"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- Status Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_status_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_status_ready"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- History Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_history_title"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/input_field_background"
        android:padding="12dp">

        <TextView
            android:id="@+id/historyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_tts_history_empty"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

    <!-- Instructions Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_instructions_title"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_tts_instructions"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:lineSpacingExtra="2dp" />

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/white">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_description"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Grammar Control Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_grammar_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <Button
            android:id="@+id/btnSetTimeGrammar"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_bnf_set_time_grammar"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnSetNumberGrammar"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_bnf_set_number_grammar"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btnClearGrammar"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_bnf_clear_grammar"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnBack"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:text="@string/realwear_bnf_back"
            android:textSize="14sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- BNF Description View (Hidden but used for grammar) -->
    <TextView
        android:id="@+id/bnfDescriptionView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        android:contentDescription="" />

    <!-- Status Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_status_section"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_status_ready"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- Command History Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/realwear_bnf_history_section"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <Button
            android:id="@+id/btnClearHistory"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="@string/realwear_bnf_clear_history"
            android:textSize="12sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:paddingStart="12dp"
            android:paddingEnd="12dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/input_field_background"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/commandHistoryView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_bnf_no_commands"
            android:textSize="14sp"
            android:textColor="@color/dark_gray"
            android:lineSpacingExtra="2dp"
            android:fontFamily="monospace" />

    </ScrollView>

    <!-- Instructions Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_instructions_title"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_bnf_instructions"
        android:textSize="14sp"
        android:textColor="@color/dark_gray"
        android:lineSpacingExtra="2dp" />

</LinearLayout>


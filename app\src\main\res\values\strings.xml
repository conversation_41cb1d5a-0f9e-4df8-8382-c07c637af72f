<resources>
    <string name="select_language">Select Language</string>
    <string name="english">English</string>
    <string name="chinese">Chinese</string>
    <string name="app_name">Standard Template</string>
    <string name="welcome">Welcome!</string>
    <string name="username">username</string>
    <string name="pass">Password</string>
    <string name="btnLogin">Login</string>
    <string name="setting">Setting</string>
    <string name="hlRegister">Register? Click Here</string>
    <string name="newTicketTitle">View New Ticket Details</string>
    <string name="txtmachineId">"Machine ID: "</string>
    <string name="machStatus">"Machine Status: "</string>
    <string name="txtErrorMsg">"Error Message: "</string>
    <string name="txtCreatedAt">"Created At: "</string>
    <string name="txtCreator">"Created By: "</string>
    <string name="txtStatus">"Status: "</string>
    <string name="txtMachDowntime">"Machine Downtime: "</string>
    <string name="btnAccept">Accept</string>
    <string name="btnBack">Back</string>
    <string name="chgLanguage">Change Language</string>
    <string name="language_english">English</string>
    <string name="language_chinese">Chinese</string>
    <string name="region">"Region: "</string>
    <string name="btnCreate">Create +</string>
    <string name="acceptedTicketList">Accepted Ticket List</string>
    <string name="logout">Logout</string>
    <string name="change_language">Language Change</string>
    <string name="change_language_content1">Change language to </string>
    <string name="change_language_content2">Restart Application is required</string>
    <string name="loginSuc">Login Successful</string>
    <string name="welcMsg">Welcome Back!</string>
    <string name="loginFailed">Login Failed</string>
    <string name="loginFailedMsg">Wrong Username or Password!</string>
    <string name="getUserFailed">Something Wrong</string>
    <string name="getUserFailedMsg">Failed to get user information!</string>
    <string name="connectionFailed">Connection Failed</string>
    <string name="connectionFailedMsg">"Network Error: "</string>
    <string name="logoutMsg">, Are you sure you want to logout?</string>
    <string name="success">Success</string>
    <string name="failed">Failed</string>
    <string name="createSuccess">Ticket Created Successfully!</string>
    <string name="failedMsg">Something Wrong! Please Try Again!</string>
    <string name="updConnection">Url Connection Updated To:</string>
    <string name="ConnectionError">URL cannot be empty, must start with http and end with /</string>
    <string name="updateSuccess">You Have Accepted 1 Ticket!</string>
    <string name="UrlConnection">URLConnection</string>
    <string name="insUrlCon">Enter URL</string>
    <string name="save">Save</string>
    <string name="createTicket">Create New Ticket</string>
    <string name="machineID">Enter Machine ID</string>
    <string name="machineStat">Enter Machine Status</string>
    <string name="ErrorMsg">Enter Error Message</string>
    <string name="cancel">Cancel</string>
    <string name="confirm">Confirm</string>
    <string name="actionStr">Confirm Action</string>
    <string name="confirmationStr">Are you sure you want to proceed?</string>
    <string name="register">Register An Account</string>
    <string name="fullname">Fullname</string>
    <string name="btnRegister">Register</string>
    <string name="hlLogin">Have an account? Click Here</string>
    <string name="close">Close</string>
    <string name="reminder">Reminder</string>
    <string name="reminderMsg">Username or Password cannot be empty!</string>
    <string name="reminderMsg2">All fields must be filled!</string>
    <string name="proceedLogin">Please Proceed to Login!</string>
    <string name="language">"Language: "</string>
    <string name="exitMsg">Are You Sure Want to Exit From App?</string>
    <string name="exit">Exit</string>

    <!-- Login Error Code -->
    <string name="L_001">L_001</string>
    <string name="L_002">L_002</string>
    <string name="L_003">L_003</string>
    <string name="errorCode_L_001">Invalid login credential.</string>
    <string name="errorCode_L_002">Username is required.</string>
    <string name="errorCode_L_003">Password is required.</string>

<!--    Register Error Code-->
    <string name="R_001">R_001</string>
    <string name="errorCode_R_001">Register Failed.</string>

    <!--    Connection Error Code-->
    <string name="C_001">C_001</string>
    <string name="errorCode_C_001">Server connection error!</string>
<!--    User info error code-->
    <string name="U_001">U_001</string>
    <string name="errorCode_U_001">Failed to Retrieve User Info.</string>

<!--    Error code Create Ticket-->
    <string name="CT_001">T_001</string>
    <string name="errorCode_CT_001">Ticket Creation Failed.</string>

<!--    Error Code Accept Ticket-->
    <string name="AT_001">AT_001</string>
    <string name="errorCode_AT_001">Ticket Accept Failed.</string>

<!--    Error Code Done Ticket-->
    <string name="DT_001">DT_001</string>
    <string name="errorCode_DT_001">Failed to submit request! Please Try Again!</string>
    <string name="F_001">F_001</string>
    <string name="errorCode_F_001">Failed to get Device Token!</string>

    <!-- RealWear Action Button功能相关字符串 -->
    <string name="realwear_action_button_title">RealWear Action Button Demo</string>
    <string name="realwear_action_button_description">Press the action button on your RealWear device to test the functionality</string>
    <string name="realwear_action_button_status">Action Button Status</string>
    <string name="realwear_action_button_status_text">Button Status: Ready</string>
    <string name="realwear_action_button_instructions">Instructions:\n• Press the action button to see visual feedback\n• The icon will turn red when pressed\n• Release the button to return to normal state\n• This demo shows how to capture RealWear hardware events</string>

    <!-- RealWear主界面相关字符串 -->
    <string name="realwear_main_title">RealWear Development Center</string>
    <string name="realwear_main_description">Test and explore RealWear device capabilities integrated with the ticket management system</string>
    <string name="realwear_camera_title">Camera Applet (Coming Soon)</string>
    <string name="realwear_document_title">Document Viewer (Coming Soon)</string>
    <string name="realwear_speech_title">Speech Recognition (Coming Soon)</string>
    <string name="realwear_camera_coming_soon">Camera functionality will be available soon</string>
    <string name="realwear_document_coming_soon">Document viewer functionality will be available soon</string>
    <string name="realwear_speech_coming_soon">Speech recognition functionality will be available soon</string>
    <string name="realwear_back_to_main">Back to Main System</string>
    <string name="realwear_back_to_main_msg">Return to the main ticket management system?</string>
    <string name="realwear_status_info">Status: RealWear integration COMPLETE! ✨\nCompatibility: Kotlin implementation successful\nAll Features: Action ✓, Camera ✓, Document ✓, Movie ✓, Barcode ✓, Keyboard ✓, Speech ✓, TTS ✓, Microphone ✓, Audio ✓, Help ✓, BNF ✓</string>
    <string name="realwear_main_button">🔧 RealWear Development</string>

    <!-- RealWear Camera Applet strings -->
    <string name="realwear_camera_applet_title">RealWear Camera Applet Demo</string>
    <string name="realwear_camera_applet_description">Test camera functionality including photo capture and video recording on RealWear devices</string>
    <string name="realwear_camera_preview_label">Captured Media Preview</string>
    <string name="realwear_camera_preview_description">Preview of captured photo or video</string>
    <string name="realwear_camera_preview_instruction">Photos and video thumbnails will appear here after capture</string>
    <string name="realwear_camera_photo_section">📷 Photo Capture</string>
    <string name="realwear_camera_bitmap_photo">Capture Bitmap Photo (Preview)</string>
    <string name="realwear_camera_file_provider_photo">Capture Full Resolution Photo</string>
    <string name="realwear_camera_video_section">🎥 Video Recording</string>
    <string name="realwear_camera_basic_video">Record Basic Video</string>
    <string name="realwear_camera_file_provider_video">Record HD Video to Gallery</string>

    <!-- RealWear Document Applet strings -->
    <string name="realwear_document_applet_title">RealWear Document Viewer Demo</string>
    <string name="realwear_document_applet_description">Open and view documents and images using external document viewers on RealWear devices</string>
    <string name="realwear_document_pdf_section">📄 PDF Documents</string>
    <string name="realwear_document_pdf_description">Open PDF documents with page and zoom control</string>
    <string name="realwear_document_open_pdf">Open Sample PDF Document</string>
    <string name="realwear_document_image_section">🖼️ Image Documents</string>
    <string name="realwear_document_image_description">View images and graphics in full resolution</string>
    <string name="realwear_document_open_image">Open Sample Image Document</string>
    <string name="realwear_document_instructions_title">Instructions</string>
    <string name="realwear_document_instructions">• Tap buttons to open sample documents\n• Documents open in external viewers\n• PDF opens with specific page and zoom settings\n• Sample files are copied from app assets to storage</string>
    <string name="realwear_document_copy_failed">Failed to copy sample documents from assets</string>
    <string name="realwear_document_file_not_found">%s document file not found or not accessible</string>
    <string name="realwear_document_viewer_not_available">No suitable %s viewer available on device</string>

    <!-- RealWear Movie Applet strings -->
    <string name="realwear_movie_applet_title">RealWear Movie Viewer Demo</string>
    <string name="realwear_movie_applet_description">Play videos using external movie viewers on RealWear devices</string>
    <string name="realwear_movie_mp4_section">🎬 MP4 Video</string>
    <string name="realwear_movie_mp4_description">Play MP4 video files in high quality</string>
    <string name="realwear_movie_play_mp4">Play Sample MP4 Video</string>
    <string name="realwear_movie_avi_section">🎥 AVI Video</string>
    <string name="realwear_movie_avi_description">Play AVI video files with system player</string>
    <string name="realwear_movie_play_avi">Play Sample AVI Video</string>
    <string name="realwear_movie_instructions_title">Instructions</string>
    <string name="realwear_movie_instructions">• Tap buttons to play sample videos\n• Videos open in external players\n• Supports various video formats (MP4, AVI, etc.)\n• Sample files are copied from app assets to storage</string>
    <string name="realwear_movie_preview_title">Video Preview</string>
    <string name="realwear_movie_preview_description">Video thumbnail placeholder</string>
    <string name="realwear_movie_preview_instruction">Video thumbnails would appear here</string>
    <string name="realwear_movie_copy_failed">Failed to copy sample videos from assets</string>
    <string name="realwear_movie_file_not_found">%s video file not found or not accessible</string>
    <string name="realwear_movie_player_not_available">No suitable %s video player available on device</string>

    <!-- RealWear Barcode Applet strings -->
    <string name="realwear_barcode_applet_title">RealWear Barcode Scanner Demo</string>
    <string name="realwear_barcode_applet_description">Scan various barcode formats using the built-in RealWear barcode scanner</string>
    <string name="realwear_barcode_scan_options">📱 Scanning Options</string>
    <string name="realwear_barcode_scan_default">Scan Barcode (QR, DataMatrix, EAN/UPC)</string>
    <string name="realwear_barcode_scan_qr_only">Scan QR Code Only</string>
    <string name="realwear_barcode_scan_all_formats">Scan All Formats (Including Code 128)</string>
    <string name="realwear_barcode_clear_results">Clear Results</string>
    <string name="realwear_barcode_results_title">📋 Scan Results</string>
    <string name="realwear_barcode_ready_to_scan">Ready to scan...\n\nTap one of the scan buttons above to start scanning barcodes.\n\nResults will appear here.</string>
    <string name="realwear_barcode_instructions_title">Instructions</string>
    <string name="realwear_barcode_instructions">• Choose scanning mode based on barcode type\n• Point camera at barcode when scanner opens\n• Scanner will automatically detect and decode\n• Results show barcode data, length, and scan time\n• Supports QR codes, DataMatrix, EAN/UPC, and Code 128</string>
    <string name="realwear_barcode_scanner_not_available">RealWear barcode scanner not available on this device</string>
    <string name="realwear_barcode_no_data">No barcode data received</string>
    <string name="realwear_barcode_scan_cancelled">Barcode scanning cancelled by user</string>
    <string name="realwear_barcode_scan_failed">Barcode scanning failed</string>
    <string name="realwear_barcode_scan_success">✅ Barcode Scanned Successfully!</string>
    <string name="realwear_barcode_result_label">Scanned Data:</string>
    <string name="realwear_barcode_result_length">Data Length:</string>
    <string name="realwear_barcode_characters">characters</string>
    <string name="realwear_barcode_scan_time">Scan Time:</string>

    <!-- RealWear Keyboard and Dictation Applet strings -->
    <string name="realwear_keyboard_dictation_title">RealWear Keyboard &amp; Dictation Demo</string>
    <string name="realwear_keyboard_dictation_description">Test voice dictation and keyboard input methods on RealWear devices</string>
    <string name="realwear_keyboard_dictation_input_methods">🎤 Input Methods</string>
    <string name="realwear_keyboard_dictation_voice_input">Voice Dictation (Speech-to-Text)</string>
    <string name="realwear_keyboard_dictation_keyboard_input">Software Keyboard Input</string>
    <string name="realwear_keyboard_dictation_clear">Clear Text</string>
    <string name="realwear_keyboard_dictation_hide_keyboard">Hide Keyboard</string>
    <string name="realwear_keyboard_dictation_text_input">📝 Text Input Area</string>
    <string name="realwear_keyboard_dictation_hint">Type or dictate your text here...</string>
    <string name="realwear_keyboard_dictation_status_label">Status:</string>
    <string name="realwear_keyboard_dictation_status_ready">Status: Ready for input</string>
    <string name="realwear_keyboard_dictation_instructions">• Voice Dictation: Tap to open RealWear speech-to-text\n• Keyboard: Tap to show software keyboard\n• Text will appear in the input area above\n• Use Clear to remove all text\n• Use Hide Keyboard to dismiss the soft keyboard</string>
    <string name="realwear_keyboard_dictation_ready">Ready for input</string>
    <string name="realwear_keyboard_dictation_launching">Launching dictation...</string>
    <string name="realwear_keyboard_dictation_not_available">RealWear dictation not available on this device</string>
    <string name="realwear_keyboard_dictation_keyboard_shown">Software keyboard displayed</string>
    <string name="realwear_keyboard_dictation_keyboard_error">Failed to show keyboard</string>
    <string name="realwear_keyboard_dictation_keyboard_hidden">Keyboard hidden</string>
    <string name="realwear_keyboard_dictation_text_cleared">Text cleared</string>
    <string name="realwear_keyboard_dictation_no_text">No text received from dictation</string>
    <string name="realwear_keyboard_dictation_success">Dictation successful (%d characters)</string>
    <string name="realwear_keyboard_dictation_cancelled">Dictation cancelled by user</string>
    <string name="realwear_keyboard_dictation_failed">Dictation failed</string>

    <!-- Speech Recognizer -->
    <string name="realwear_speech_recognizer_title">RealWear Speech Recognizer Demo</string>
    <string name="realwear_speech_recognizer_description">Register and test voice commands using RealWear\'s Advanced Speech Recognition (ASR)</string>
    <string name="realwear_speech_recognizer_current_command">🎤 Current Voice Command</string>
    <string name="realwear_speech_recognizer_no_command_yet">No command spoken yet</string>
    <string name="realwear_speech_recognizer_status_section">📊 Recognition Status</string>
    <string name="realwear_speech_recognizer_status_ready">Status: Ready for voice commands</string>
    <string name="realwear_speech_recognizer_clear_results">Clear Results</string>
    <string name="realwear_speech_recognizer_back">Back</string>
    <string name="realwear_speech_recognizer_results_title">📋 Command History</string>
    <string name="realwear_speech_recognizer_ready_message">Ready to receive voice commands...\n\nSpeak one of the following commands:\n• \"Quantity 1\"\n• \"Quantity 2\"\n• \"Quantity 3\"\n• \"Show Status\"\n• \"Clear Results\"\n• \"Go Back\"\n\nResults will appear here.</string>
    <string name="realwear_speech_recognizer_instructions_title">Instructions</string>
    <string name="realwear_speech_recognizer_instructions">• Speak clearly using the registered voice commands\n• Commands are case-insensitive and processed automatically\n• \"Show Status\" displays current statistics\n• \"Clear Results\" removes all command history\n• \"Go Back\" returns to the main RealWear menu\n• All commands are logged with timestamps</string>
    <string name="realwear_speech_recognizer_ready">Ready for voice commands</string>
    <string name="realwear_speech_recognizer_commands_registered">Voice commands registered successfully</string>
    <string name="realwear_speech_recognizer_setup_failed">Failed to setup voice commands</string>
    <string name="realwear_speech_recognizer_listening">Listening for voice commands</string>
    <string name="realwear_speech_recognizer_register_failed">Failed to register speech receiver</string>
    <string name="realwear_speech_recognizer_no_command">No command received</string>
    <string name="realwear_speech_recognizer_processing_error">Error processing voice command</string>
    <string name="realwear_speech_recognizer_command_received">Command received: %s</string>
    <string name="realwear_speech_recognizer_status_commands_received">Status: %d commands received so far</string>
    <string name="realwear_speech_recognizer_results_cleared">Results cleared successfully</string>
    <string name="realwear_speech_recognizer_status_label">Status: %s</string>

    <!-- Text to Speech -->
    <string name="realwear_tts_title">RealWear Text to Speech Demo</string>
    <string name="realwear_tts_description">Convert text to speech using RealWear\'s built-in TTS service</string>
    <string name="realwear_tts_input_section">📝 Text Input</string>
    <string name="realwear_tts_input_hint">Enter text to be spoken aloud...</string>
    <string name="realwear_tts_speak_text">Speak Input Text</string>
    <string name="realwear_tts_speak_sample">Speak Sample Text</string>
    <string name="realwear_tts_clear_history">Clear History</string>
    <string name="realwear_tts_back">Back</string>
    <string name="realwear_tts_status_section">📊 TTS Status</string>
    <string name="realwear_tts_status_ready">Status: Ready for text to speech</string>
    <string name="realwear_tts_history_title">📋 Speech History</string>
    <string name="realwear_tts_history_empty">No speech requests yet...\n\nEnter text above and tap \"Speak Input Text\" to start.\n\nOr tap \"Speak Sample Text\" to hear a demo.\n\nHistory will appear here.</string>
    <string name="realwear_tts_instructions_title">Instructions</string>
    <string name="realwear_tts_instructions">• Enter custom text in the input field and tap \"Speak Input Text\"\n• Tap \"Speak Sample Text\" to hear a predefined message\n• All speech requests are logged with timestamps\n• Use \"Clear History\" to remove all records\n• TTS service runs in background - you\'ll hear audio output</string>
    <string name="realwear_tts_sample_text">Hello! This is a RealWear Text to Speech demonstration. The TTS service is working correctly and can convert any text into spoken words. This feature is very useful for hands-free operations on RealWear devices.</string>
    <string name="realwear_tts_ready">Ready for text to speech</string>
    <string name="realwear_tts_service_ready">TTS service ready and listening</string>
    <string name="realwear_tts_register_failed">Failed to register TTS receiver</string>
    <string name="realwear_tts_empty_text">Please enter some text to speak</string>
    <string name="realwear_tts_empty_text_status">No text provided for speech</string>
    <string name="realwear_tts_speaking">Speaking: %s...</string>
    <string name="realwear_tts_start_failed">Failed to start text to speech</string>
    <string name="realwear_tts_history_cleared">Speech history cleared successfully</string>
    <string name="realwear_tts_finished">TTS completed (ID: %d)</string>
    <string name="realwear_tts_processing_error">Error processing TTS event</string>
    <string name="realwear_tts_status_label">Status: %s</string>

    <!-- Microphone Release -->
    <string name="realwear_microphone_title">RealWear Microphone Release Demo</string>
    <string name="realwear_microphone_description">Control microphone access and disable voice recognition for full audio input control</string>
    <string name="realwear_microphone_options_section">🎛️ Control Options</string>
    <string name="realwear_microphone_hide_text">Hide system message when mic is off</string>
    <string name="realwear_microphone_custom_message">Use custom mute message</string>
    <string name="realwear_microphone_release">Release Microphone</string>
    <string name="realwear_microphone_restore">Restore Microphone</string>
    <string name="realwear_microphone_clear_history">Clear History</string>
    <string name="realwear_microphone_back">Back</string>
    <string name="realwear_microphone_status_section">📊 Microphone Status</string>
    <string name="realwear_microphone_status_ready">Status: Ready for microphone control</string>
    <string name="realwear_microphone_history_title">📋 Event History</string>
    <string name="realwear_microphone_history_empty">No microphone events yet...\n\nTap \"Release Microphone\" to disable voice recognition.\n\nTap \"Restore Microphone\" to re-enable voice recognition.\n\nEvent history will appear here.</string>
    <string name="realwear_microphone_instructions_title">Instructions</string>
    <string name="realwear_microphone_instructions">• \"Release Microphone\" disables voice recognition completely\n• \"Restore Microphone\" re-enables voice recognition\n• Configure display options with the switches above\n• All microphone events are logged with timestamps\n• Use \"Clear History\" to remove all event records</string>
    <string name="realwear_microphone_custom_message_text">Mic is Off. Press Action Button to Resume</string>
    <string name="realwear_microphone_ready">Ready for microphone control</string>
    <string name="realwear_microphone_listening">Listening for microphone events</string>
    <string name="realwear_microphone_register_failed">Failed to register microphone receiver</string>
    <string name="realwear_microphone_releasing">Releasing microphone...</string>
    <string name="realwear_microphone_release_failed">Failed to release microphone</string>
    <string name="realwear_microphone_restoring">Restoring microphone...</string>
    <string name="realwear_microphone_restore_failed">Failed to restore microphone</string>
    <string name="realwear_microphone_released">Microphone released successfully</string>
    <string name="realwear_microphone_system_request">System requested microphone access</string>
    <string name="realwear_microphone_processing_error">Error processing microphone event</string>
    <string name="realwear_microphone_history_cleared">Microphone event history cleared</string>
    <string name="realwear_microphone_status_label">Status: %s</string>

    <!-- Audio Capture -->
    <string name="realwear_audio_title">RealWear Audio Capture Demo</string>
    <string name="realwear_audio_description">Record and playback audio at different sample rates and channel configurations</string>
    <string name="realwear_audio_sample_rate_section">🎵 Sample Rate</string>
    <string name="realwear_audio_8khz">8 KHz</string>
    <string name="realwear_audio_16khz">16 KHz</string>
    <string name="realwear_audio_44khz">44.1 KHz</string>
    <string name="realwear_audio_48khz">48 KHz</string>
    <string name="realwear_audio_channels_section">🔊 Channels</string>
    <string name="realwear_audio_mono">Mono (1 Channel)</string>
    <string name="realwear_audio_stereo">Stereo (2 Channels)</string>
    <string name="realwear_audio_output_section">📁 Output File</string>
    <string name="realwear_audio_filename_placeholder">Select settings to see output filename</string>
    <string name="realwear_audio_record">🔴 Record (10s)</string>
    <string name="realwear_audio_playback">▶️ Playback</string>
    <string name="realwear_audio_status_section">📊 Status</string>
    <string name="realwear_audio_status_ready">Status: Ready for audio recording</string>
    <string name="realwear_audio_instructions_title">Instructions</string>
    <string name="realwear_audio_instructions">• Select desired sample rate (8-48 KHz)\n• Choose mono or stereo recording\n• Tap \"Record\" to capture 10 seconds of audio\n• Tap \"Playback\" to play the recorded audio\n• Files are saved as WAV format in Music folder\n• Higher sample rates provide better quality but larger files\n• Ensure microphone permissions are granted</string>
    <string name="realwear_audio_back">Back</string>
    <string name="realwear_audio_ready">Ready for audio recording</string>
    <string name="realwear_audio_recording">Recording audio...</string>
    <string name="realwear_audio_playing">Playing audio...</string>
    <string name="realwear_audio_record_complete">Recording completed successfully</string>
    <string name="realwear_audio_playback_complete">Playback completed</string>
    <string name="realwear_audio_record_init_failed">Failed to initialize AudioRecorder</string>
    <string name="realwear_audio_record_failed">Audio recording failed</string>
    <string name="realwear_audio_playback_failed">Audio playback failed</string>
    <string name="realwear_audio_no_recording">No recording found. Please record audio first</string>
    <string name="realwear_audio_file_create_error">Error creating audio file</string>
    <string name="realwear_audio_file_write_error">Error writing audio file</string>
    <string name="realwear_audio_permissions_error">Audio recording permissions are required</string>
    <string name="realwear_audio_permissions_denied">Audio permissions denied</string>
    <string name="realwear_audio_permissions_granted">Audio permissions granted</string>
    <string name="realwear_audio_status_label">Status: %s</string>

    <!-- Help Menu -->
    <string name="realwear_help_title">RealWear Help Menu Demo</string>
    <string name="realwear_help_description">Add custom voice commands to the RealWear Show Help dialog</string>
    <string name="realwear_help_control_section">🎛️ Command Management</string>
    <string name="realwear_help_add_commands">Add Sample Commands</string>
    <string name="realwear_help_clear_commands">Clear Commands</string>
    <string name="realwear_help_refresh_commands">Refresh Commands</string>
    <string name="realwear_help_back">Back</string>
    <string name="realwear_help_status_section">📊 Status</string>
    <string name="realwear_help_status_ready">Status: Ready to manage help commands</string>
    <string name="realwear_help_commands_section">📋 Current Help Commands</string>
    <string name="realwear_help_no_commands">No custom commands registered.\n\nTap \"Add Sample Commands\" to register voice commands in the help system.</string>
    <string name="realwear_help_instructions_title">Instructions</string>
    <string name="realwear_help_instructions">• Tap \"Add Sample Commands\" to register custom voice commands\n• Use \"Clear Commands\" to remove all custom commands\n• \"Refresh Commands\" resends the current command list\n• Say \"Show Help\" on your RealWear device to see registered commands\n• Commands are automatically cleared when leaving this screen</string>
    <string name="realwear_help_command_navigate">Navigate Menu</string>
    <string name="realwear_help_command_select">Select Item</string>
    <string name="realwear_help_command_back">Go Back</string>
    <string name="realwear_help_command_home">Go Home</string>
    <string name="realwear_help_command_record">Start Recording</string>
    <string name="realwear_help_command_playback">Start Playback</string>
    <string name="realwear_help_command_settings">Open Settings</string>
    <string name="realwear_help_command_help">Show Help</string>
    <string name="realwear_help_ready">Ready to manage help commands</string>
    <string name="realwear_help_commands_active">Help commands are active</string>
    <string name="realwear_help_commands_added">Added %d new commands to help menu</string>
    <string name="realwear_help_add_failed">Failed to add help commands</string>
    <string name="realwear_help_commands_cleared">Help commands cleared successfully</string>
    <string name="realwear_help_clear_failed">Failed to clear help commands</string>
    <string name="realwear_help_commands_refreshed">Refreshed %d help commands</string>
    <string name="realwear_help_refresh_failed">Failed to refresh help commands</string>
    <string name="realwear_help_commands_list">Active Commands:\n\n%s</string>
    <string name="realwear_help_status_label">Status: %s</string>

    <!-- BNF Grammar -->
    <string name="realwear_bnf_title">RealWear BNF Grammar Demo</string>
    <string name="realwear_bnf_description">Use Backus-Naur Form to define complex voice command patterns</string>
    <string name="realwear_bnf_grammar_section">🔤 Grammar Management</string>
    <string name="realwear_bnf_set_time_grammar">Set Time Grammar</string>
    <string name="realwear_bnf_set_number_grammar">Set Number Grammar</string>
    <string name="realwear_bnf_clear_grammar">Clear Grammar</string>
    <string name="realwear_bnf_back">Back</string>
    <string name="realwear_bnf_status_section">📊 Status</string>
    <string name="realwear_bnf_status_ready">Grammar: None | Status: Ready</string>
    <string name="realwear_bnf_history_section">📋 Command History</string>
    <string name="realwear_bnf_clear_history">Clear</string>
    <string name="realwear_bnf_no_commands">No voice commands received yet.\n\nSet a grammar and try speaking:\n• Time Grammar: \"3 30\" or \"15\"\n• Number Grammar: \"Select Five\" or \"Go to Ten\"</string>
    <string name="realwear_bnf_instructions_title">Instructions</string>
    <string name="realwear_bnf_instructions">• \"Set Time Grammar\": Accepts hour (1-24) + optional minute (1-59)\n  Example: \"3\", \"15 30\", \"22 45\"\n• \"Set Number Grammar\": Accepts action + number (0-20)\n  Example: \"Select Five\", \"Go to Ten\", \"Choose Three\"\n• \"Clear Grammar\": Removes custom grammar and uses default commands\n• Voice commands appear in real-time in the history below</string>
    <string name="realwear_bnf_ready">Ready to set BNF grammar</string>
    <string name="realwear_bnf_listening">Listening for voice commands</string>
    <string name="realwear_bnf_time_grammar_set">Time grammar activated - Try: \"3 30\" or \"15\"</string>
    <string name="realwear_bnf_number_grammar_set">Number grammar activated - Try: \"Select Five\"</string>
    <string name="realwear_bnf_grammar_cleared">Grammar cleared - Using default commands</string>
    <string name="realwear_bnf_grammar_set_failed">Failed to set grammar</string>
    <string name="realwear_bnf_grammar_clear_failed">Failed to clear grammar</string>
    <string name="realwear_bnf_history_cleared">Command history cleared</string>
    <string name="realwear_bnf_command_received">Command: %s</string>
    <string name="realwear_bnf_command_toast">Voice Command: \"%s\"</string>
    <string name="realwear_bnf_command_history">Recent Commands:\n\n%s</string>
    <string name="realwear_bnf_status_format">Grammar: %s | Status: %s</string>

    <!-- RealWear开发者入口 -->
    <string name="realwear_dev_entrance">RealWear Development</string>
    <string name="realwear_dev_entrance_desc">Direct access to RealWear functionality testing</string>

</resources>
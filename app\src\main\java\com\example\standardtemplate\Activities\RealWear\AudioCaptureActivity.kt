/**
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * 
 * Contact <EMAIL> for further information about the use of this code.
 */

package com.example.standardtemplate.Activities.RealWear

import android.Manifest
import android.content.pm.PackageManager
import android.media.*
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Environment
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R
import java.io.*
import java.util.concurrent.TimeUnit

/**
 * Activity that shows how to capture audio at different frequencies on a RealWear device.
 * This demo allows recording and playback of audio with various sample rates and channel configurations.
 */
class AudioCaptureActivity : BaseActivity(), Runnable {

    companion object {
        private const val TAG = "AudioCaptureActivity"
        private const val SAMPLE_LENGTH_SECONDS = 10
        private const val PERMISSION_REQUEST_CODE = 1001
    }

    // UI Components
    private lateinit var mRecordButton: Button
    private lateinit var mPlaybackButton: Button
    private lateinit var mProgressBar: ProgressBar
    private lateinit var mFilenameLabel: TextView
    private lateinit var mStatusLabel: TextView
    private lateinit var btnBack: Button

    // Channel selection
    private lateinit var mMonoButton: RadioButton
    private lateinit var mStereoButton: RadioButton

    // Sample rate selection
    private lateinit var mRate8Button: RadioButton
    private lateinit var mRate16Button: RadioButton
    private lateinit var mRate44Button: RadioButton
    private lateinit var mRate48Button: RadioButton

    // Audio configuration
    private var mSampleRate: Int = 44100
    private var mSampleRateString: String = "44KHz"
    private var mChannels: Short = 2
    private var mChannelsString: String = "stereo"

    // Audio recording
    private var mAudioRecorder: AudioRecord? = null
    private var mRecordingThread: Thread? = null
    private var mPlaybackAudioTimer: CountDownTimer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set fullscreen mode for RealWear device compatibility
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        setContentView(R.layout.activity_realwear_audio_capture)

        initializeViews()
        checkAudioPermissions()
    }

    /**
     * Initialize all view components
     */
    private fun initializeViews() {
        mRecordButton = findViewById(R.id.recordButton)
        mPlaybackButton = findViewById(R.id.playbackButton)
        mProgressBar = findViewById(R.id.progressBar)
        mFilenameLabel = findViewById(R.id.fileTextLabel)
        mStatusLabel = findViewById(R.id.statusLabel)
        btnBack = findViewById(R.id.btnBack)

        mMonoButton = findViewById(R.id.monoButton)
        mStereoButton = findViewById(R.id.stereoButton)

        mRate8Button = findViewById(R.id.rate8Button)
        mRate16Button = findViewById(R.id.rate16Button)
        mRate44Button = findViewById(R.id.rate44Button)
        mRate48Button = findViewById(R.id.rate48Button)

        // Setup click listeners
        mRecordButton.setOnClickListener { onStartRecord() }
        mPlaybackButton.setOnClickListener { onStartPlayback() }
        btnBack.setOnClickListener { finish() }

        // Setup radio button listeners
        mMonoButton.setOnClickListener { onChannelsChanged(it) }
        mStereoButton.setOnClickListener { onChannelsChanged(it) }

        mRate8Button.setOnClickListener { onSampleRateChanged(it) }
        mRate16Button.setOnClickListener { onSampleRateChanged(it) }
        mRate44Button.setOnClickListener { onSampleRateChanged(it) }
        mRate48Button.setOnClickListener { onSampleRateChanged(it) }
    }

    /**
     * Check and request audio recording permissions
     */
    private fun checkAudioPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) 
            != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(
                this, 
                arrayOf(Manifest.permission.RECORD_AUDIO), 
                PERMISSION_REQUEST_CODE
            )
        }
    }

    override fun onResume() {
        super.onResume()

        resetUI()
        
        // Set default values
        mRate44Button.performClick()
        mStereoButton.performClick()
        
        updateStatus(getString(R.string.realwear_audio_ready))
    }

    override fun onPause() {
        super.onPause()

        stopRecording()
        stopPlayback()
    }

    /**
     * Reset UI to initial state
     */
    private fun resetUI() {
        setButtonState(true)
        mProgressBar.visibility = View.INVISIBLE
        mFilenameLabel.text = ""
        mProgressBar.progress = 0
    }

    /**
     * Start audio recording
     */
    private fun onStartRecord() {
        try {
            Log.d(TAG, "Starting audio recording")
            
            val channelConfig = if (mChannels.toInt() == 1) {
                AudioFormat.CHANNEL_IN_MONO
            } else {
                AudioFormat.CHANNEL_IN_STEREO
            }

            mAudioRecorder = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                mSampleRate,
                channelConfig,
                AudioFormat.ENCODING_PCM_16BIT,
                4096
            )

            if (mAudioRecorder?.state == AudioRecord.STATE_UNINITIALIZED) {
                Toast.makeText(this, R.string.realwear_audio_record_init_failed, Toast.LENGTH_LONG).show()
                updateStatus(getString(R.string.realwear_audio_record_init_failed))
                return
            }

            mProgressBar.progress = 0
            mProgressBar.visibility = View.VISIBLE
            setButtonState(false)
            updateStatus(getString(R.string.realwear_audio_recording))

            mAudioRecorder?.startRecording()

            mRecordingThread = Thread(this).apply {
                name = "Recording Thread"
                priority = Thread.MIN_PRIORITY
                start()
            }

            Log.d(TAG, "Audio recording started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start recording", e)
            Toast.makeText(this, R.string.realwear_audio_record_failed, Toast.LENGTH_LONG).show()
            updateStatus(getString(R.string.realwear_audio_record_failed))
            resetUI()
        }
    }

    /**
     * Start audio playback
     */
    private fun onStartPlayback() {
        val recordingFile = getRecordingFile()
        
        if (!recordingFile.exists()) {
            Toast.makeText(this, R.string.realwear_audio_no_recording, Toast.LENGTH_SHORT).show()
            updateStatus(getString(R.string.realwear_audio_no_recording))
            return
        }

        try {
            Log.d(TAG, "Starting audio playback")
            
            val mediaPlayer = MediaPlayer().apply {
                setDataSource(recordingFile.absolutePath)
                prepare()
                start()
            }

            setButtonState(false)
            mProgressBar.progress = 0
            mProgressBar.visibility = View.VISIBLE
            updateStatus(getString(R.string.realwear_audio_playing))

            mPlaybackAudioTimer = object : CountDownTimer(
                mediaPlayer.duration.toLong(),
                TimeUnit.SECONDS.toMillis(1)
            ) {
                override fun onTick(millisUntilFinished: Long) {
                    val duration = mediaPlayer.duration.toFloat()
                    val msPlayed = duration - millisUntilFinished
                    val progress = msPlayed / duration * mProgressBar.max
                    mProgressBar.progress = progress.toInt()
                }

                override fun onFinish() {
                    setButtonState(true)
                    mProgressBar.visibility = View.INVISIBLE
                    updateStatus(getString(R.string.realwear_audio_playback_complete))
                }
            }.start()

            Log.d(TAG, "Audio playback started successfully")
        } catch (e: IOException) {
            Log.e(TAG, "Playback error", e)
            Toast.makeText(this, R.string.realwear_audio_playback_failed, Toast.LENGTH_LONG).show()
            updateStatus(getString(R.string.realwear_audio_playback_failed))
            resetUI()
        }
    }

    /**
     * Thread function for recording audio
     */
    override fun run() {
        val bitsPerSecond = if (mAudioRecorder?.audioFormat == AudioFormat.ENCODING_PCM_16BIT) 16 else 8
        val outputStream = ByteArrayOutputStream()
        val buffer = ByteArray(4096 * 2)

        val startTime = System.currentTimeMillis()
        var seconds = 0f

        try {
            while (seconds < SAMPLE_LENGTH_SECONDS &&
                   mAudioRecorder != null &&
                   mAudioRecorder?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                
                val bytesRead = mAudioRecorder?.read(buffer, 0, buffer.size) ?: 0
                if (bytesRead > 0) {
                    outputStream.write(buffer, 0, bytesRead)
                }

                seconds = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - startTime).toFloat()
                updateProgressBar(seconds)
            }

            // Stop recording and save file
            mAudioRecorder?.let {
                it.stop()
                it.release()
                mAudioRecorder = null

                writeWAVFile(getRecordingFile(), outputStream, bitsPerSecond)
            }

            runOnUiThread {
                mProgressBar.visibility = View.INVISIBLE
                setButtonState(true)
                updateStatus(getString(R.string.realwear_audio_record_complete))
                Toast.makeText(this@AudioCaptureActivity, R.string.realwear_audio_record_complete, Toast.LENGTH_SHORT).show()
            }

            Log.d(TAG, "Audio recording completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Recording thread error", e)
            runOnUiThread {
                Toast.makeText(this@AudioCaptureActivity, R.string.realwear_audio_record_failed, Toast.LENGTH_LONG).show()
                updateStatus(getString(R.string.realwear_audio_record_failed))
                resetUI()
            }
        }
    }

    /**
     * Update progress bar during recording
     */
    private fun updateProgressBar(seconds: Float) {
        runOnUiThread {
            val progress = seconds / SAMPLE_LENGTH_SECONDS * mProgressBar.max
            mProgressBar.progress = progress.toInt()
        }
    }

    /**
     * Handle channel selection changes
     */
    private fun onChannelsChanged(view: View) {
        when (view) {
            mMonoButton -> {
                mChannels = 1
                mChannelsString = "mono"
            }
            mStereoButton -> {
                mChannels = 2
                mChannelsString = "stereo"
            }
        }
        updateFileName()
    }

    /**
     * Handle sample rate selection changes
     */
    private fun onSampleRateChanged(view: View) {
        when (view) {
            mRate8Button -> {
                mSampleRate = 8000
                mSampleRateString = "8KHz"
            }
            mRate16Button -> {
                mSampleRate = 16000
                mSampleRateString = "16KHz"
            }
            mRate44Button -> {
                mSampleRate = 44100
                mSampleRateString = "44KHz"
            }
            mRate48Button -> {
                mSampleRate = 48000
                mSampleRateString = "48KHz"
            }
        }
        updateFileName()
    }

    /**
     * Update the filename display
     */
    private fun updateFileName() {
        mFilenameLabel.text = getRecordingFile().toString()
    }

    /**
     * Get the recording file based on current settings
     */
    private fun getRecordingFile(): File {
        val filename = "audio_test_${mSampleRateString}_$mChannelsString.wav"
        val mediaStorageDir = getExternalFilesDir(Environment.DIRECTORY_MUSIC)
        return File(mediaStorageDir, filename)
    }

    /**
     * Enable or disable UI controls
     */
    private fun setButtonState(isEnabled: Boolean) {
        mRate8Button.isEnabled = isEnabled
        mRate16Button.isEnabled = isEnabled
        mRate44Button.isEnabled = isEnabled
        mRate48Button.isEnabled = isEnabled

        mMonoButton.isEnabled = isEnabled
        mStereoButton.isEnabled = isEnabled

        mPlaybackButton.isEnabled = isEnabled
        mRecordButton.isEnabled = isEnabled
    }

    /**
     * Stop audio recording
     */
    private fun stopRecording() {
        mAudioRecorder?.let {
            try {
                it.stop()
                it.release()
                mAudioRecorder = null
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping recording", e)
            }
        }

        mRecordingThread?.let {
            try {
                it.join()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop recording thread", e)
            }
        }
        mRecordingThread = null
    }

    /**
     * Stop audio playback
     */
    private fun stopPlayback() {
        mPlaybackAudioTimer?.cancel()
        mPlaybackAudioTimer = null
    }

    /**
     * Update status display
     */
    private fun updateStatus(status: String) {
        mStatusLabel.text = getString(R.string.realwear_audio_status_label, status)
    }

    /**
     * Write recorded audio data to WAV file
     */
    private fun writeWAVFile(file: File, outputStream: ByteArrayOutputStream, bitsPerSample: Int) {
        val audioData = outputStream.toByteArray()

        try {
            FileOutputStream(file).use { fos ->
                DataOutputStream(fos).use { dos ->
                    writeWaveFileHeader(dos, audioData.size, bitsPerSample)
                    fos.write(audioData)
                    fos.flush()
                }
            }
            
            rescanFolder(file.parent ?: "")
            Log.d(TAG, "WAV file written successfully: ${file.absolutePath}")
        } catch (e: FileNotFoundException) {
            Log.e(TAG, "Error creating wav file", e)
            Toast.makeText(this, R.string.realwear_audio_file_create_error, Toast.LENGTH_LONG).show()
        } catch (e: IOException) {
            Log.e(TAG, "Error writing wav file", e)
            Toast.makeText(this, R.string.realwear_audio_file_write_error, Toast.LENGTH_LONG).show()
        }
    }

    /**
     * Write WAV file header
     */
    private fun writeWaveFileHeader(dos: DataOutputStream, audioLen: Int, bitsPerSample: Int) {
        val wavHeaderSize = 44
        val totalDataLen = audioLen + wavHeaderSize - 8
        val waveFormatPcm: Short = 0x1
        val byteSize = 8

        // RIFF chunk marker and size
        dos.writeBytes("RIFF")
        dos.write(intToByteArray(totalDataLen))
        dos.writeBytes("WAVE")

        // Format chunk marker and size
        dos.writeBytes("fmt ")
        dos.write(intToByteArray(16))

        // Format data
        dos.write(shortToByteArray(waveFormatPcm))
        dos.write(shortToByteArray(mChannels))
        dos.write(intToByteArray(mSampleRate))

        val blockAlign = mChannels * bitsPerSample / byteSize
        val avgBytesPerSec = mSampleRate * blockAlign

        dos.write(intToByteArray(avgBytesPerSec))
        dos.write(shortToByteArray(blockAlign.toShort()))
        dos.write(shortToByteArray(bitsPerSample.toShort()))

        // Data chunk marker and size
        dos.writeBytes("data")
        dos.write(intToByteArray(audioLen))
    }

    /**
     * Scan folder to ensure Android finds new files
     */
    private fun rescanFolder(dest: String) {
        try {
            val files = File(dest).listFiles { pathname -> pathname.isFile }
            files?.let {
                val paths = it.map { file -> file.absolutePath }.toTypedArray()
                MediaScannerConnection.scanFile(this, paths, null, null)
            }

            // Recursively scan subfolders
            val folders = File(dest).listFiles { pathname -> pathname.isDirectory }
            folders?.forEach { folder ->
                rescanFolder(folder.absolutePath)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error rescanning folder", e)
        }
    }

    /**
     * Convert int to byte array
     */
    private fun intToByteArray(data: Int): ByteArray {
        return byteArrayOf(
            (data and 0xff).toByte(),
            ((data shr 8) and 0xff).toByte(),
            ((data shr 16) and 0xff).toByte(),
            ((data shr 24) and 0xff).toByte()
        )
    }

    /**
     * Convert short to byte array
     */
    private fun shortToByteArray(data: Short): ByteArray {
        return byteArrayOf(
            (data.toInt() and 0xff).toByte(),
            ((data.toInt() shr 8) and 0xff).toByte()
        )
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.any { it != PackageManager.PERMISSION_GRANTED }) {
                Toast.makeText(this, R.string.realwear_audio_permissions_error, Toast.LENGTH_LONG).show()
                updateStatus(getString(R.string.realwear_audio_permissions_denied))
                finish()
            } else {
                updateStatus(getString(R.string.realwear_audio_permissions_granted))
            }
        }
    }
}

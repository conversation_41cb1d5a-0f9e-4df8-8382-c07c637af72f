{"logs": [{"outputFile": "com.example.standardtemplate.app-mergeDebugResources-44:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6da3c2cc95131e6e0714e67e2bba3b1b\\transformed\\core-1.15.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3547,3649,3747,3844,3952,4063,12293", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3542,3644,3742,3839,3947,4058,4180,12389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b5fd8a1debeeacbb6bf1c31739d4dca\\transformed\\play-services-basement-18.1.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5556", "endColumns": "128", "endOffsets": "5680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\85d1553e1b933cd2e13c79366a035914\\transformed\\play-services-base-18.0.1\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4495,4602,4775,4912,5019,5180,5314,5440,5685,5855,5963,6138,6276,6438,6622,6687,6754", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "4597,4770,4907,5014,5175,5309,5435,5551,5850,5958,6133,6271,6433,6617,6682,6749,6831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6b3ff4ec66daf8ab6ce732fa9c600777\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,11974", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,12051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\838052c77a74c243fbc236b2a83bb29c\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3092,3170,3256,3356,4185,4286,4412,6836,6901,6966,7066,7136,7195,7293,7355,7419,7478,7550,7613,7667,7784,7841,7903,7957,8029,8164,8247,8326,8422,8505,8583,8724,8808,8890,9038,9128,9206,9259,9318,9384,9455,9534,9605,9688,9764,9842,9914,9987,10091,10180,10252,10346,10445,10519,10591,10692,10742,10827,10893,10983,11072,11134,11198,11261,11328,11444,11557,11666,11771,11828,11891,12056,12141,12215", "endLines": "5,33,34,35,36,37,45,46,47,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,131,132,133", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "312,3087,3165,3251,3351,3443,4281,4407,4490,6896,6961,7061,7131,7190,7288,7350,7414,7473,7545,7608,7662,7779,7836,7898,7952,8024,8159,8242,8321,8417,8500,8578,8719,8803,8885,9033,9123,9201,9254,9313,9379,9450,9529,9600,9683,9759,9837,9909,9982,10086,10175,10247,10341,10440,10514,10586,10687,10737,10822,10888,10978,11067,11129,11193,11256,11323,11439,11552,11661,11766,11823,11886,11969,12136,12210,12288"}}]}]}
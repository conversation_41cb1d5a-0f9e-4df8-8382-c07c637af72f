package com.example.standardtemplate.Activities.NewTickets

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.widget.Button
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.enableEdgeToEdge
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity
import com.example.standardtemplate.Activities.Login_Setting.LoginActivity
import com.example.standardtemplate.Activities.RealWear.RealWearMainActivity
import com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter
import com.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog
import com.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListener
import com.example.standardtemplate.Dialogs.ConfirmationDialog.showConfirmDialog
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.ApiClient
import com.example.standardtemplate.Libraries.ApiInterface
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Libraries.StandardFunction.SharedPref
import com.example.standardtemplate.Models.NewTicketInfo
import com.example.standardtemplate.Models.NewTicketListResponse
import com.example.standardtemplate.R
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class NewTicketListActivity : BaseActivity(), CreateDialogListener {
    private lateinit var btnLogout : Button
    private lateinit var btnCreate : Button
    private lateinit var btnAcceptedTicketList : Button
    private lateinit var btnRealWear : Button
    private lateinit var rvTicketList: RecyclerView
    private lateinit var newTicketAdapter: NewTicketAdapter

    private lateinit var apiService : ApiInterface
    private lateinit var sharedPref: SharedPref

    private lateinit var handlerThread:HandlerThread
    private lateinit var handler: Handler

    private lateinit var call_new_ticket_list: Call<List<NewTicketListResponse>>
    private val newTicketList: MutableList<NewTicketListResponse> = mutableListOf()
    private val token = UserInfoManager.token!!.toString()
    private val username = UserInfoManager.username.toString()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge()
        setContentView(R.layout.activity_new_ticket_list)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        Log.d("User Information", "username: $username \ntoken: $token")

        sharedPref = SharedPref(this)

        //declare component
        rvTicketList = findViewById(R.id.rvTicketList)
        btnLogout = findViewById(R.id.btnLogout)
        btnCreate= findViewById(R.id.btnCreate)
        btnAcceptedTicketList = findViewById(R.id.btnAcceptedTicketList)
        btnRealWear = findViewById(R.id.btnRealWear)

        newTicketAdapter = NewTicketAdapter(newTicketList, this@NewTicketListActivity)
        rvTicketList.adapter = newTicketAdapter
        rvTicketList.layoutManager = GridLayoutManager(this, 2)

        /*
        handle onBackPress function
        prompt out a logout confirmation dialog for the user when onBackPressed is triggered.
        after the user logout, store the specific lastVisit record into the sharedPref 'ticketMonitoring', and clear all the sharedPref in 'AuthPrefs'
        then stop the monitoring service and navigate to the login activity
         */
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                showConfirmDialog(this@NewTicketListActivity, getString(R.string.logout), "$username" + getString(R.string.logoutMsg), onConfirm = {
                    val intent = Intent(applicationContext, LoginActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    }

                    //store lastvisit into sharedPref
                    val lastVisit = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(
                        Date()
                    )

                    sharedPref.recordLastVisit(this@NewTicketListActivity, lastVisit)
                    UserInfoManager.clearUserData() // call function in UserInfoManager to clear user data
//                    stopMonitoringService(this@NewTicketListActivity)

                    startActivity(intent)
                    finish()
                })
            }
        })

        //handle the logout button
        btnLogout.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }

        // triggered the createDialog when btnCreate is clicked
        btnCreate.setOnClickListener {
            val createDialog = CreateDialog(this, this)
            createDialog.showCreateDialog(this)
        }

        //navigate to the accepted List page
        btnAcceptedTicketList.setOnClickListener {
            val intent = Intent(this, AcceptedTicketListActivity::class.java)
            startActivity(intent)
        }

        //navigate to the RealWear functionality page
        btnRealWear.setOnClickListener {
            val intent = Intent(this, RealWearMainActivity::class.java)
            startActivity(intent)
        }
    }

    /**
     * Fetch new ticket list from the API
     * @param token The authentication token , passed when the function called
     * @return new ticket list that get from the API
     */
    private fun fetchNewTicketList() {
        handlerThread = HandlerThread("NotificationThread").apply { start() }
        handler = Handler(handlerThread.looper)
        apiService = ApiClient.getAuthenticatedClient(this, token).create(ApiInterface::class.java)

        //handle the API call for getting the latest ticket list
        call_new_ticket_list = apiService.getTicketList()
        call_new_ticket_list.enqueue(object : Callback<List<NewTicketListResponse>> {
            override fun onResponse(call: Call<List<NewTicketListResponse>>, response: Response<List<NewTicketListResponse>>) {
                response.body()?.let { newTicketAdapter.updateData(it) }
                handler.postDelayed({
                    call_new_ticket_list =
                        call.clone() // retrofit call does not allow  multiple execution, cloning it will duplicate the call
                    call_new_ticket_list.enqueue(this) // proceed execute the call with handling it using the same codes
                },5000) // delay for 3 sec bfr the action called agn
            }

            //handle network error
            override fun onFailure(call: Call<List<NewTicketListResponse>>, t: Throwable) {
                if (!call.isCanceled) {
                    Log.e("Network Error", "Network Error: ${t.message}")
                    Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()
                    call_new_ticket_list =
                        call.clone() // retrofit call does not allow  multiple execution, cloning it will duplicate the call
                    call_new_ticket_list.enqueue(this) // proceed execute the call with handling it using the same codes

                }
            }
        })
    }

    /*
    Start to run this function when user reenter to this activity
     */
    override fun onResume() {
        super.onResume()
        if (token.isNotEmpty()) {
            val lastVisit = sharedPref.getLastVisitRecord(this@NewTicketListActivity)
            fetchNewTicketList()

            Log.d("onResume", "Resuming Ticket Monitoring with Last Visit: $lastVisit")
        }
    }

    /*
    stop the repeat service when the user is exited from this activity
     */
    override fun onPause() {
        super.onPause()
        // terminate the API calling
        if (call_new_ticket_list.isExecuted && !call_new_ticket_list.isCanceled)
            call_new_ticket_list.cancel()

        // quit the handler
        handler.removeCallbacksAndMessages(null)
        handlerThread.looper?.quit()
    }

    /**
     * ** Work together with the CreateDialog **
     *
     * submit the ticket information that want to create to the API
     * @param id the machineId of the ticket, passed from the CreateDialog
     * @param status the machineStatus of the ticket, passed from the CreateDialog
     * @param error the errorMsg of the ticket, passed from the CreateDialog
     * @param downtime the machineDowntime of the ticket, passed from the CreateDialog
     *
     * @return successful dialog with message 'Ticket created successfully' if the ticket is created successfully
     * OR
     * @return failed dialog with message 'Something Wrong! Please Try Again!' if the ticket is not created successfully
     */
    override fun onSubmitTicket(id: String, status: String, error: String, downtime: String) {
        val apiService = ApiClient.getAuthenticatedClient(this, token).create(ApiInterface::class.java)
        val newTicketInfo = NewTicketInfo(id, status, error, downtime) // store newTicket information into NewTicketDto as the API request @body

        //calling create ticket function by passing the ticket info to the API
        apiService.createNewTicket(newTicketInfo).enqueue(object : retrofit2.Callback<String> {
            override fun onResponse(call: Call<String>, response: Response<String>) {
                if (response.isSuccessful) {
                    showMessageDialog(this@NewTicketListActivity, getString(R.string.success), getString(R.string.createSuccess) +" \n "+ getString(R.string.txtmachineId) + "${newTicketInfo.machineId}")
                } else {
                    val errorBody = response.errorBody()?.string()
                    showMessageDialog(this@NewTicketListActivity, getString(R.string.CT_001), getString(R.string.errorCode_CT_001))
                    Log.e("TICKET FAILED", "Ticket Failed: $errorBody") // see log for more information when the ticket is not created successfully
                    Log.e("TICKET FAILED", "Ticket Failed: $errorBody") // see log for more information when the ticket is not created successfully
                }
            }

            // handle network error
            override fun onFailure(call: Call<String>, t: Throwable) {
                Log.e("Network Error", "Network Error: ${t.message}")
                Toast.makeText(applicationContext, "${getString(R.string.C_001)}: ${getString(R.string.errorCode_C_001)}", Toast.LENGTH_SHORT).show()
            }
        })
    }

}
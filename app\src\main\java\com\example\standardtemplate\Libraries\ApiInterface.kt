package com.example.standardtemplate.Libraries

import com.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto
import com.example.standardtemplate.Models.FcmTokenRequest
import com.example.standardtemplate.Models.LoginInfo
import com.example.standardtemplate.Models.LoginResponse
import com.example.standardtemplate.Models.NewTicketInfo
import com.example.standardtemplate.Models.NewTicketListResponse
import com.example.standardtemplate.Models.RegisterInfo
import com.example.standardtemplate.Models.RegisterResponse
import com.example.standardtemplate.Models.TicketDone
import com.example.standardtemplate.Models.UserDetails
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query

interface ApiInterface {
    @Headers(
        "Content-Type: application/json", "Accept: text/plain"
    )

    @POST("Auth/login")
    fun Login(@Body loginInfo: LoginInfo): Call<LoginResponse>

    @GET("Auth/me")
    fun getUserInfo(): Call<UserDetails>

    @POST("Auth/Register")
    fun registerAccount(@Body registerInfo: RegisterInfo): Call<RegisterResponse>

    @GET("Ticket/GetNewTickets")
    fun getTicketList(): Call<List<NewTicketListResponse>>

    @POST("Ticket/GenerateTicket")
    fun createNewTicket(@Body newTicketInfo: NewTicketInfo): Call<String>

    @GET("Ticket/GetAcceptedTickets")
    fun getAcceptedTickets(): Call<List<AcceptedTicketDto>>

    @GET("Ticket/AttendTicket")
    fun acceptTicket(@Query("ticket_id") id: Int): Call<Void>

    @GET("Ticket/CheckLatest")
    fun checkLatestTicket(@Query("last_date") lastVisit: String): Call<Int>

    @POST("Ticket/DoneTicket")
    fun solvedTicket(@Body ticketDone: TicketDone): Call<Void>

    //Start FCM
    @POST("ticket/sendFcmToken")
    fun sendToken(@Body tokenReq : FcmTokenRequest) : Call<Void>
}
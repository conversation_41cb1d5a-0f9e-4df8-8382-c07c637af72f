package com.example.standardtemplate.Activities.NewTickets.Adapter


import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity
import com.example.standardtemplate.Models.NewTicketListResponse
import com.example.standardtemplate.R

class NewTicketAdapter(
    private val ticketList: MutableList<NewTicketListResponse>, //used to store the ticket record into list
    private val context: Context // Add a context parameter
) : RecyclerView.Adapter<NewTicketAdapter.TicketViewHolder>() {

    class TicketViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) { //cardview information
        val txtMachineId: TextView = itemView.findViewById(R.id.txtMachineId)
        val txtErrorMsg: TextView = itemView.findViewById(R.id.txtErrorMsg)
        val txtMachineStatus: TextView = itemView.findViewById(R.id.txtMachineStatus)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketViewHolder {
        val itemView = LayoutInflater.from(parent.context)
            .inflate(R.layout.recycle_view_new_ticket_list, parent, false)
        return TicketViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: TicketViewHolder, position: Int) {
        val ticket = ticketList[position]

        holder.txtMachineId.text =  context.getString(R.string.txtmachineId)+"\n${ticket.machineId}"
        holder.txtErrorMsg.text = context.getString(R.string.txtErrorMsg) + "\n${ticket.errorMsg}"
        holder.txtMachineStatus.text = context.getString(R.string.machStatus) +"\n${ticket.machineStatus}"

        holder.itemView.setOnClickListener { //passed the ticket information when the item in clicked
            val context = holder.itemView.context
            val intent = Intent(context, NewTicketDetailsActivity::class.java).apply {
                putExtra("id", ticket.id.toString())
                putExtra("creator", ticket.creator)
                putExtra("status", ticket.status)
                putExtra("createdAt", ticket.createdAt)
                putExtra("machineId", ticket.machineId)
                putExtra("errorMsg", ticket.errorMsg)
                putExtra("machineStatus", ticket.machineStatus)
                putExtra("machineDowntime", ticket.machineDowntime)
            }
            context.startActivity(intent)
        }
    }

    override fun getItemCount() = ticketList.size

    //update the data of the adapter by comparing the newlist and also the ticketlist(old list), update only if there is any difference
    fun updateData(newList: List<NewTicketListResponse>) {
        val diff = newList.map{x->x.id}.toSet() != ticketList.map{x->x.id}.toSet() // check if any id is not same
        if (diff) {
            ticketList.clear()
            ticketList.addAll(newList)
            notifyDataSetChanged()
        }
    }

}

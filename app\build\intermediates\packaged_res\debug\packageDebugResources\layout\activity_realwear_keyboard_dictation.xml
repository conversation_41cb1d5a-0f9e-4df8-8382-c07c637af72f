<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Keyboard and Dictation Demo Layout -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@color/white"
    tools:context=".Activities.RealWear.KeyboardDictationActivity">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_keyboard_dictation_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_keyboard_dictation_description"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:lineSpacingMultiplier="1.2" />

    <!-- Input Methods Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_keyboard_dictation_input_methods"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp" />

        <!-- Dictation Button -->
        <Button
            android:id="@+id/btnLaunchDictation"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_keyboard_dictation_voice_input"
            android:textSize="16sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- Keyboard Button -->
        <Button
            android:id="@+id/btnLaunchKeyboard"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_keyboard_dictation_keyboard_input"
            android:textSize="16sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- Control Buttons Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnClearText"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                android:text="@string/realwear_keyboard_dictation_clear"
                android:textSize="14sp"
                android:background="@drawable/dialog_background"
                android:textColor="@color/black"
                android:layout_marginEnd="4dp"
                android:gravity="center" />

            <Button
                android:id="@+id/btnHideKeyboard"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="1"
                android:text="@string/realwear_keyboard_dictation_hide_keyboard"
                android:textSize="14sp"
                android:background="@drawable/dialog_background"
                android:textColor="@color/black"
                android:layout_marginStart="4dp"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- Text Input Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_keyboard_dictation_text_input"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp" />

        <!-- Text Input Field -->
        <EditText
            android:id="@+id/dictationField"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:padding="12dp"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:gravity="top|start"
            android:inputType="textMultiLine|textCapSentences"
            android:hint="@string/realwear_keyboard_dictation_hint"
            android:textColorHint="@android:color/darker_gray"
            android:scrollbars="vertical"
            android:minLines="3"
            android:maxLines="10" />

    </LinearLayout>

    <!-- Status and Instructions Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <!-- Status Display -->
        <TextView
            android:id="@+id/txtStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_keyboard_dictation_status_ready"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <!-- Instructions -->
        <TextView
            android:id="@+id/txtInstructions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_keyboard_dictation_instructions"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:lineSpacingMultiplier="1.3" />

    </LinearLayout>

    <!-- Back Button -->
    <Button
        android:id="@+id/btnBack"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="@string/btnBack"
        android:textSize="16sp"
        android:background="@drawable/dialog_background"
        android:textColor="@color/black"
        android:drawableStart="@drawable/ic_back"
        android:drawablePadding="8dp"
        android:gravity="center" />

</LinearLayout>


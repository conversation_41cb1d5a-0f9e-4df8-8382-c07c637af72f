<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="AT_001">A_001</string>
    <string name="CT_001">T_001</string>
    <string name="C_001">C_001</string>
    <string name="ConnectionError">URL不能为空，且必须以http开头, /结尾</string>
    <string name="DT_001">DT_001</string>
    <string name="ErrorMsg">输入错误信息</string>
    <string name="F_001">F_001</string>
    <string name="L_001">L_001</string>
    <string name="L_002">L_002</string>
    <string name="L_003">L_003</string>
    <string name="R_001">R_001</string>
    <string name="U_001">U_001</string>
    <string name="UrlConnection">URL 连接</string>
    <string name="acceptedTicketList">已接受工单列表</string>
    <string name="actionStr">确认操作</string>
    <string name="app_name">标准模板</string>
    <string name="btnAccept">接受</string>
    <string name="btnBack">返回</string>
    <string name="btnCreate">创建 +</string>
    <string name="btnLogin">登录</string>
    <string name="btnRegister">注册</string>
    <string name="cancel">取消</string>
    <string name="change_language">更改语言</string>
    <string name="change_language_content1">更改语言为</string>
    <string name="change_language_content2">需要重启应用</string>
    <string name="chgLanguage">更改语言</string>
    <string name="chinese">中文</string>
    <string name="close">关闭</string>
    <string name="confirm">确认</string>
    <string name="confirmationStr">您确定要继续吗？</string>
    <string name="connectionFailed">连接失败</string>
    <string name="connectionFailedMsg">"网络错误: "</string>
    <string name="createSuccess">工单创建成功！</string>
    <string name="createTicket">创建新工单</string>
    <string name="english">英语</string>
    <string name="errorCode_AT_001">接受工单失败。</string>
    <string name="errorCode_CT_001">创建工单失败。</string>
    <string name="errorCode_C_001">服务器连接错误！</string>
    <string name="errorCode_DT_001">提交请求失败！请再试一次！</string>
    <string name="errorCode_F_001">获取设备令牌失败！</string>
    <string name="errorCode_L_001">登录凭证无效。</string>
    <string name="errorCode_L_002">用户名为必填项。</string>
    <string name="errorCode_L_003">密码为必填项。</string>
    <string name="errorCode_R_001">注册失败。</string>
    <string name="errorCode_U_001">获取用户信息失败。</string>
    <string name="exit">退出</string>
    <string name="exitMsg">确定退出此应用？</string>
    <string name="failed">失败</string>
    <string name="failedMsg">发生错误！请重试！</string>
    <string name="fullname">全名</string>
    <string name="getUserFailed">发生错误</string>
    <string name="getUserFailedMsg">获取用户信息失败！</string>
    <string name="hlLogin">已有账户？点击这里</string>
    <string name="hlRegister">注册？点击这里</string>
    <string name="insUrlCon">输入 URL</string>
    <string name="language">"语言： "</string>
    <string name="language_chinese">中文</string>
    <string name="language_english">英语</string>
    <string name="loginFailed">登录失败</string>
    <string name="loginFailedMsg">用户名或密码错误！</string>
    <string name="loginSuc">登录成功</string>
    <string name="logout">退出登录</string>
    <string name="logoutMsg">，您确定要退出登录吗？</string>
    <string name="machStatus">"机器状态: "</string>
    <string name="machineID">输入机器编号</string>
    <string name="machineStat">输入机器状态</string>
    <string name="newTicketTitle">查看新工单详情</string>
    <string name="pass">密码</string>
    <string name="proceedLogin">请前往登录页面继续登录！</string>
    <string name="realwear_action_button_description">按下您RealWear设备上的操作按钮来测试功能</string>
    <string name="realwear_action_button_instructions">使用说明：\n• 按下操作按钮查看视觉反馈\n• 按下时图标会变成红色\n• 释放按钮恢复正常状态\n• 此演示展示如何捕获RealWear硬件事件</string>
    <string name="realwear_action_button_status">操作按钮状态</string>
    <string name="realwear_action_button_status_text">按钮状态：就绪</string>
    <string name="realwear_action_button_title">RealWear 操作按钮演示</string>
    <string name="realwear_audio_16khz">16 KHz</string>
    <string name="realwear_audio_44khz">44.1 KHz</string>
    <string name="realwear_audio_48khz">48 KHz</string>
    <string name="realwear_audio_8khz">8 KHz</string>
    <string name="realwear_audio_back">返回</string>
    <string name="realwear_audio_channels_section">🔊 声道</string>
    <string name="realwear_audio_description">以不同采样率和声道配置录制和播放音频</string>
    <string name="realwear_audio_file_create_error">创建音频文件时出错</string>
    <string name="realwear_audio_file_write_error">写入音频文件时出错</string>
    <string name="realwear_audio_filename_placeholder">选择设置以查看输出文件名</string>
    <string name="realwear_audio_instructions">• 选择所需的采样率（8-48 KHz）\n• 选择单声道或立体声录制\n• 点击"录制"捕获10秒音频\n• 点击"播放"播放录制的音频\n• 文件以WAV格式保存在音乐文件夹中\n• 更高的采样率提供更好的质量但文件更大\n• 确保已授予麦克风权限</string>
    <string name="realwear_audio_instructions_title">使用说明</string>
    <string name="realwear_audio_mono">单声道（1声道）</string>
    <string name="realwear_audio_no_recording">未找到录音。请先录制音频</string>
    <string name="realwear_audio_output_section">📁 输出文件</string>
    <string name="realwear_audio_permissions_denied">音频权限被拒绝</string>
    <string name="realwear_audio_permissions_error">需要音频录制权限</string>
    <string name="realwear_audio_permissions_granted">音频权限已授予</string>
    <string name="realwear_audio_playback">▶️ 播放</string>
    <string name="realwear_audio_playback_complete">播放完成</string>
    <string name="realwear_audio_playback_failed">音频播放失败</string>
    <string name="realwear_audio_playing">正在播放音频...</string>
    <string name="realwear_audio_ready">准备音频录制</string>
    <string name="realwear_audio_record">🔴 录制（10秒）</string>
    <string name="realwear_audio_record_complete">录制成功完成</string>
    <string name="realwear_audio_record_failed">音频录制失败</string>
    <string name="realwear_audio_record_init_failed">音频录制器初始化失败</string>
    <string name="realwear_audio_recording">正在录制音频...</string>
    <string name="realwear_audio_sample_rate_section">🎵 采样率</string>
    <string name="realwear_audio_status_label">状态：%s</string>
    <string name="realwear_audio_status_ready">状态：准备音频录制</string>
    <string name="realwear_audio_status_section">📊 状态</string>
    <string name="realwear_audio_stereo">立体声（2声道）</string>
    <string name="realwear_audio_title">RealWear 音频录制演示</string>
    <string name="realwear_back_to_main">返回主系统</string>
    <string name="realwear_back_to_main_msg">返回到主票务管理系统？</string>
    <string name="realwear_barcode_applet_description">使用RealWear内置条码扫描器扫描各种条码格式</string>
    <string name="realwear_barcode_applet_title">RealWear 条码扫描器演示</string>
    <string name="realwear_barcode_characters">字符</string>
    <string name="realwear_barcode_clear_results">清除结果</string>
    <string name="realwear_barcode_instructions">• 根据条码类型选择扫描模式\n• 扫描器打开时将相机对准条码\n• 扫描器将自动检测和解码\n• 结果显示条码数据、长度和扫描时间\n• 支持二维码、DataMatrix、EAN/UPC和Code 128</string>
    <string name="realwear_barcode_instructions_title">使用说明</string>
    <string name="realwear_barcode_no_data">未收到条码数据</string>
    <string name="realwear_barcode_ready_to_scan">准备扫描...\n\n点击上方的扫描按钮开始扫描条码。\n\n结果将显示在这里。</string>
    <string name="realwear_barcode_result_label">扫描数据：</string>
    <string name="realwear_barcode_result_length">数据长度：</string>
    <string name="realwear_barcode_results_title">📋 扫描结果</string>
    <string name="realwear_barcode_scan_all_formats">扫描所有格式（包括Code 128）</string>
    <string name="realwear_barcode_scan_cancelled">用户取消条码扫描</string>
    <string name="realwear_barcode_scan_default">扫描条码（QR、DataMatrix、EAN/UPC）</string>
    <string name="realwear_barcode_scan_failed">条码扫描失败</string>
    <string name="realwear_barcode_scan_options">📱 扫描选项</string>
    <string name="realwear_barcode_scan_qr_only">仅扫描二维码</string>
    <string name="realwear_barcode_scan_success">✅ 条码扫描成功！</string>
    <string name="realwear_barcode_scan_time">扫描时间：</string>
    <string name="realwear_barcode_scanner_not_available">此设备上不可用RealWear条码扫描器</string>
    <string name="realwear_bnf_back">返回</string>
    <string name="realwear_bnf_clear_grammar">清除语法</string>
    <string name="realwear_bnf_clear_history">清除</string>
    <string name="realwear_bnf_command_history">最近命令：\n\n%s</string>
    <string name="realwear_bnf_command_received">命令：%s</string>
    <string name="realwear_bnf_command_toast">语音命令："%s"</string>
    <string name="realwear_bnf_description">使用巴科斯-瑙尔范式定义复杂的语音命令模式</string>
    <string name="realwear_bnf_grammar_clear_failed">清除语法失败</string>
    <string name="realwear_bnf_grammar_cleared">语法已清除 - 使用默认命令</string>
    <string name="realwear_bnf_grammar_section">🔤 语法管理</string>
    <string name="realwear_bnf_grammar_set_failed">设置语法失败</string>
    <string name="realwear_bnf_history_cleared">命令历史已清除</string>
    <string name="realwear_bnf_history_section">📋 命令历史</string>
    <string name="realwear_bnf_instructions">• "设置时间语法"：接受小时 (1-24) + 可选分钟 (1-59)\n  示例："3"、"15 30"、"22 45"\n• "设置数字语法"：接受动作 + 数字 (0-20)\n  示例："Select Five"、"Go to Ten"、"Choose Three"\n• "清除语法"：删除自定义语法并使用默认命令\n• 语音命令会实时显示在下方历史记录中</string>
    <string name="realwear_bnf_instructions_title">使用说明</string>
    <string name="realwear_bnf_listening">正在监听语音命令</string>
    <string name="realwear_bnf_no_commands">尚未接收到语音命令。\n\n设置语法并尝试说话：\n• 时间语法："3 30" 或 "15"\n• 数字语法："Select Five" 或 "Go to Ten"</string>
    <string name="realwear_bnf_number_grammar_set">数字语法已激活 - 尝试："Select Five"</string>
    <string name="realwear_bnf_ready">准备设置 BNF 语法</string>
    <string name="realwear_bnf_set_number_grammar">设置数字语法</string>
    <string name="realwear_bnf_set_time_grammar">设置时间语法</string>
    <string name="realwear_bnf_status_format">语法: %s | 状态: %s</string>
    <string name="realwear_bnf_status_ready">语法：无 | 状态：就绪</string>
    <string name="realwear_bnf_status_section">📊 状态</string>
    <string name="realwear_bnf_time_grammar_set">时间语法已激活 - 尝试："3 30" 或 "15"</string>
    <string name="realwear_bnf_title">RealWear BNF 语法演示</string>
    <string name="realwear_camera_applet_description">测试RealWear设备上的相机功能，包括拍照和录像</string>
    <string name="realwear_camera_applet_title">RealWear 相机小程序演示</string>
    <string name="realwear_camera_basic_video">录制基础视频</string>
    <string name="realwear_camera_bitmap_photo">拍摄位图照片（预览）</string>
    <string name="realwear_camera_coming_soon">相机功能即将推出</string>
    <string name="realwear_camera_file_provider_photo">拍摄全分辨率照片</string>
    <string name="realwear_camera_file_provider_video">录制高清视频到相册</string>
    <string name="realwear_camera_photo_section">📷 拍照功能</string>
    <string name="realwear_camera_preview_description">拍摄照片或视频的预览</string>
    <string name="realwear_camera_preview_instruction">拍摄后照片和视频缩略图将显示在这里</string>
    <string name="realwear_camera_preview_label">捕获媒体预览</string>
    <string name="realwear_camera_title">相机小程序（即将推出）</string>
    <string name="realwear_camera_video_section">🎥 录像功能</string>
    <string name="realwear_dev_entrance">🥽 RealWear开发测试</string>
    <string name="realwear_dev_entrance_desc">直接进入RealWear功能测试</string>
    <string name="realwear_document_applet_description">在RealWear设备上使用外部文档查看器打开和查看文档及图像</string>
    <string name="realwear_document_applet_title">RealWear 文档查看器演示</string>
    <string name="realwear_document_coming_soon">文档查看器功能即将推出</string>
    <string name="realwear_document_copy_failed">从资源复制示例文档失败</string>
    <string name="realwear_document_file_not_found">%s文档文件未找到或无法访问</string>
    <string name="realwear_document_image_description">以全分辨率查看图像和图形</string>
    <string name="realwear_document_image_section">🖼️ 图像文档</string>
    <string name="realwear_document_instructions">• 点击按钮打开示例文档\n• 文档在外部查看器中打开\n• PDF使用特定页面和缩放设置打开\n• 示例文件从应用资源复制到存储</string>
    <string name="realwear_document_instructions_title">使用说明</string>
    <string name="realwear_document_open_image">打开示例图像文档</string>
    <string name="realwear_document_open_pdf">打开示例PDF文档</string>
    <string name="realwear_document_pdf_description">打开PDF文档并控制页面和缩放级别</string>
    <string name="realwear_document_pdf_section">📄 PDF文档</string>
    <string name="realwear_document_title">文档查看器（即将推出）</string>
    <string name="realwear_document_viewer_not_available">设备上没有合适的%s查看器</string>
    <string name="realwear_help_add_commands">添加示例命令</string>
    <string name="realwear_help_add_failed">添加帮助命令失败</string>
    <string name="realwear_help_back">返回</string>
    <string name="realwear_help_clear_commands">清除命令</string>
    <string name="realwear_help_clear_failed">清除帮助命令失败</string>
    <string name="realwear_help_command_back">返回</string>
    <string name="realwear_help_command_help">显示帮助</string>
    <string name="realwear_help_command_home">回到主页</string>
    <string name="realwear_help_command_navigate">导航菜单</string>
    <string name="realwear_help_command_playback">开始播放</string>
    <string name="realwear_help_command_record">开始录制</string>
    <string name="realwear_help_command_select">选择项目</string>
    <string name="realwear_help_command_settings">打开设置</string>
    <string name="realwear_help_commands_active">帮助命令已激活</string>
    <string name="realwear_help_commands_added">已向帮助菜单添加 %d 个新命令</string>
    <string name="realwear_help_commands_cleared">帮助命令清除成功</string>
    <string name="realwear_help_commands_list">活动命令：\n\n%s</string>
    <string name="realwear_help_commands_refreshed">已刷新 %d 个帮助命令</string>
    <string name="realwear_help_commands_section">📋 当前帮助命令</string>
    <string name="realwear_help_control_section">🎛️ 命令管理</string>
    <string name="realwear_help_description">向 RealWear 显示帮助对话框添加自定义语音命令</string>
    <string name="realwear_help_instructions">• 点击"添加示例命令"注册自定义语音命令\n• 使用"清除命令"删除所有自定义命令\n• "刷新命令"重新发送当前命令列表\n• 在您的 RealWear 设备上说"Show Help"查看注册的命令\n• 离开此屏幕时命令会自动清除</string>
    <string name="realwear_help_instructions_title">使用说明</string>
    <string name="realwear_help_no_commands">未注册自定义命令。\n\n点击"添加示例命令"在帮助系统中注册语音命令。</string>
    <string name="realwear_help_ready">准备管理帮助命令</string>
    <string name="realwear_help_refresh_commands">刷新命令</string>
    <string name="realwear_help_refresh_failed">刷新帮助命令失败</string>
    <string name="realwear_help_status_label">状态：%s</string>
    <string name="realwear_help_status_ready">状态：准备管理帮助命令</string>
    <string name="realwear_help_status_section">📊 状态</string>
    <string name="realwear_help_title">RealWear 帮助菜单演示</string>
    <string name="realwear_keyboard_dictation_cancelled">用户取消听写</string>
    <string name="realwear_keyboard_dictation_clear">清除文本</string>
    <string name="realwear_keyboard_dictation_description">在RealWear设备上测试语音听写和键盘输入方法</string>
    <string name="realwear_keyboard_dictation_failed">听写失败</string>
    <string name="realwear_keyboard_dictation_hide_keyboard">隐藏键盘</string>
    <string name="realwear_keyboard_dictation_hint">在此输入或听写您的文本...</string>
    <string name="realwear_keyboard_dictation_input_methods">🎤 输入方法</string>
    <string name="realwear_keyboard_dictation_instructions">• 语音听写：点击打开RealWear语音转文字\n• 键盘：点击显示软件键盘\n• 文本将出现在上方输入区域\n• 使用清除按钮删除所有文本\n• 使用隐藏键盘按钮关闭软键盘</string>
    <string name="realwear_keyboard_dictation_keyboard_error">显示键盘失败</string>
    <string name="realwear_keyboard_dictation_keyboard_hidden">键盘已隐藏</string>
    <string name="realwear_keyboard_dictation_keyboard_input">软件键盘输入</string>
    <string name="realwear_keyboard_dictation_keyboard_shown">软件键盘已显示</string>
    <string name="realwear_keyboard_dictation_launching">正在启动听写...</string>
    <string name="realwear_keyboard_dictation_no_text">未从听写接收到文本</string>
    <string name="realwear_keyboard_dictation_not_available">此设备上不可用RealWear听写功能</string>
    <string name="realwear_keyboard_dictation_ready">准备输入</string>
    <string name="realwear_keyboard_dictation_status_label">状态：</string>
    <string name="realwear_keyboard_dictation_status_ready">状态：准备输入</string>
    <string name="realwear_keyboard_dictation_success">听写成功（%d个字符）</string>
    <string name="realwear_keyboard_dictation_text_cleared">文本已清除</string>
    <string name="realwear_keyboard_dictation_text_input">📝 文本输入区域</string>
    <string name="realwear_keyboard_dictation_title">RealWear 键盘和听写演示</string>
    <string name="realwear_keyboard_dictation_voice_input">语音听写（语音转文字）</string>
    <string name="realwear_main_button">🔧 RealWear 开发</string>
    <string name="realwear_main_description">测试和探索与票务管理系统集成的RealWear设备功能</string>
    <string name="realwear_main_title">RealWear 开发中心</string>
    <string name="realwear_microphone_back">返回</string>
    <string name="realwear_microphone_clear_history">清除历史</string>
    <string name="realwear_microphone_custom_message">使用自定义静音消息</string>
    <string name="realwear_microphone_custom_message_text">麦克风已关闭。按操作按钮恢复</string>
    <string name="realwear_microphone_description">控制麦克风访问权限并禁用语音识别以完全控制音频输入</string>
    <string name="realwear_microphone_hide_text">麦克风关闭时隐藏系统消息</string>
    <string name="realwear_microphone_history_cleared">麦克风事件历史已清除</string>
    <string name="realwear_microphone_history_empty">尚无麦克风事件...\n\n点击"释放麦克风"禁用语音识别。\n\n点击"恢复麦克风"重新启用语音识别。\n\n事件历史将显示在这里。</string>
    <string name="realwear_microphone_history_title">📋 事件历史</string>
    <string name="realwear_microphone_instructions">• "释放麦克风"完全禁用语音识别\n• "恢复麦克风"重新启用语音识别\n• 使用上方开关配置显示选项\n• 所有麦克风事件都记录时间戳\n• 使用"清除历史"删除所有事件记录</string>
    <string name="realwear_microphone_instructions_title">使用说明</string>
    <string name="realwear_microphone_listening">正在监听麦克风事件</string>
    <string name="realwear_microphone_options_section">🎛️ 控制选项</string>
    <string name="realwear_microphone_processing_error">处理麦克风事件时出错</string>
    <string name="realwear_microphone_ready">准备麦克风控制</string>
    <string name="realwear_microphone_register_failed">麦克风接收器注册失败</string>
    <string name="realwear_microphone_release">释放麦克风</string>
    <string name="realwear_microphone_release_failed">释放麦克风失败</string>
    <string name="realwear_microphone_released">麦克风释放成功</string>
    <string name="realwear_microphone_releasing">正在释放麦克风...</string>
    <string name="realwear_microphone_restore">恢复麦克风</string>
    <string name="realwear_microphone_restore_failed">恢复麦克风失败</string>
    <string name="realwear_microphone_restoring">正在恢复麦克风...</string>
    <string name="realwear_microphone_status_label">状态：%s</string>
    <string name="realwear_microphone_status_ready">状态：准备麦克风控制</string>
    <string name="realwear_microphone_status_section">📊 麦克风状态</string>
    <string name="realwear_microphone_system_request">系统请求麦克风访问</string>
    <string name="realwear_microphone_title">RealWear 麦克风释放演示</string>
    <string name="realwear_movie_applet_description">在RealWear设备上使用外部视频播放器播放视频</string>
    <string name="realwear_movie_applet_title">RealWear 视频播放器演示</string>
    <string name="realwear_movie_avi_description">使用系统播放器播放AVI视频文件</string>
    <string name="realwear_movie_avi_section">🎥 AVI视频</string>
    <string name="realwear_movie_copy_failed">从资源复制示例视频失败</string>
    <string name="realwear_movie_file_not_found">%s视频文件未找到或无法访问</string>
    <string name="realwear_movie_instructions">• 点击按钮播放示例视频\n• 视频在外部播放器中打开\n• 支持多种视频格式（MP4、AVI等）\n• 示例文件从应用资源复制到存储</string>
    <string name="realwear_movie_instructions_title">使用说明</string>
    <string name="realwear_movie_mp4_description">播放高质量MP4视频文件</string>
    <string name="realwear_movie_mp4_section">🎬 MP4视频</string>
    <string name="realwear_movie_play_avi">播放示例AVI视频</string>
    <string name="realwear_movie_play_mp4">播放示例MP4视频</string>
    <string name="realwear_movie_player_not_available">设备上没有合适的%s视频播放器</string>
    <string name="realwear_movie_preview_description">视频缩略图占位符</string>
    <string name="realwear_movie_preview_instruction">视频缩略图将显示在这里</string>
    <string name="realwear_movie_preview_title">视频预览</string>
    <string name="realwear_speech_coming_soon">语音识别功能即将推出</string>
    <string name="realwear_speech_recognizer_back">返回</string>
    <string name="realwear_speech_recognizer_clear_results">清除结果</string>
    <string name="realwear_speech_recognizer_command_received">收到命令：%s</string>
    <string name="realwear_speech_recognizer_commands_registered">语音命令注册成功</string>
    <string name="realwear_speech_recognizer_current_command">🎤 当前语音命令</string>
    <string name="realwear_speech_recognizer_description">使用 RealWear 高级语音识别 (ASR) 注册和测试语音命令</string>
    <string name="realwear_speech_recognizer_instructions">• 使用注册的语音命令清晰地说话\n• 命令不区分大小写并自动处理\n• \"Show Status\" 显示当前统计信息\n• \"Clear Results\" 清除所有命令历史\n• \"Go Back\" 返回主 RealWear 菜单\n• 所有命令都记录时间戳</string>
    <string name="realwear_speech_recognizer_instructions_title">使用说明</string>
    <string name="realwear_speech_recognizer_listening">正在监听语音命令</string>
    <string name="realwear_speech_recognizer_no_command">未收到命令</string>
    <string name="realwear_speech_recognizer_no_command_yet">尚未说出命令</string>
    <string name="realwear_speech_recognizer_processing_error">处理语音命令时出错</string>
    <string name="realwear_speech_recognizer_ready">准备接收语音命令</string>
    <string name="realwear_speech_recognizer_ready_message">准备接收语音命令...\n\n请说出以下命令之一：\n• \"Quantity 1\"\n• \"Quantity 2\"\n• \"Quantity 3\"\n• \"Show Status\"\n• \"Clear Results\"\n• \"Go Back\"\n\n结果将在这里显示。</string>
    <string name="realwear_speech_recognizer_register_failed">语音接收器注册失败</string>
    <string name="realwear_speech_recognizer_results_cleared">结果清除成功</string>
    <string name="realwear_speech_recognizer_results_title">📋 命令历史</string>
    <string name="realwear_speech_recognizer_setup_failed">语音命令设置失败</string>
    <string name="realwear_speech_recognizer_status_commands_received">状态：目前已收到 %d 个命令</string>
    <string name="realwear_speech_recognizer_status_label">状态：%s</string>
    <string name="realwear_speech_recognizer_status_ready">状态：准备接收语音命令</string>
    <string name="realwear_speech_recognizer_status_section">📊 识别状态</string>
    <string name="realwear_speech_recognizer_title">RealWear 语音识别演示</string>
    <string name="realwear_speech_title">语音识别（即将推出）</string>
    <string name="realwear_status_info">状态：RealWear集成完成！ ✨\n兼容性：Kotlin实现成功\n所有功能：操作 ✓，相机 ✓，文档 ✓，视频 ✓，条码 ✓，键盘 ✓，语音 ✓，TTS ✓，麦克风 ✓，音频 ✓，帮助 ✓，BNF ✓</string>
    <string name="realwear_tts_back">返回</string>
    <string name="realwear_tts_clear_history">清除历史</string>
    <string name="realwear_tts_description">使用 RealWear 内置 TTS 服务将文本转换为语音</string>
    <string name="realwear_tts_empty_text">请输入一些要朗读的文本</string>
    <string name="realwear_tts_empty_text_status">未提供语音文本</string>
    <string name="realwear_tts_finished">TTS 完成（ID：%d）</string>
    <string name="realwear_tts_history_cleared">语音历史清除成功</string>
    <string name="realwear_tts_history_empty">尚无语音请求...\n\n在上方输入文本并点击"朗读输入文本"开始。\n\n或点击"朗读示例文本"听演示。\n\n历史记录将显示在这里。</string>
    <string name="realwear_tts_history_title">📋 语音历史</string>
    <string name="realwear_tts_input_hint">输入要朗读的文本...</string>
    <string name="realwear_tts_input_section">📝 文本输入</string>
    <string name="realwear_tts_instructions">• 在输入框中输入自定义文本并点击"朗读输入文本"\n• 点击"朗读示例文本"听预定义消息\n• 所有语音请求都记录时间戳\n• 使用"清除历史"删除所有记录\n• TTS 服务在后台运行 - 您将听到音频输出</string>
    <string name="realwear_tts_instructions_title">使用说明</string>
    <string name="realwear_tts_processing_error">处理 TTS 事件时出错</string>
    <string name="realwear_tts_ready">准备文本转语音</string>
    <string name="realwear_tts_register_failed">TTS 接收器注册失败</string>
    <string name="realwear_tts_sample_text">您好！这是 RealWear 文本转语音演示。TTS 服务工作正常，可以将任何文本转换为语音。此功能对于 RealWear 设备上的免提操作非常有用。</string>
    <string name="realwear_tts_service_ready">TTS 服务就绪并正在监听</string>
    <string name="realwear_tts_speak_sample">朗读示例文本</string>
    <string name="realwear_tts_speak_text">朗读输入文本</string>
    <string name="realwear_tts_speaking">正在朗读：%s...</string>
    <string name="realwear_tts_start_failed">启动文本转语音失败</string>
    <string name="realwear_tts_status_label">状态：%s</string>
    <string name="realwear_tts_status_ready">状态：准备文本转语音</string>
    <string name="realwear_tts_status_section">📊 TTS 状态</string>
    <string name="realwear_tts_title">RealWear 文本转语音演示</string>
    <string name="region">"地区: "</string>
    <string name="register">注册账户</string>
    <string name="reminder">提醒</string>
    <string name="reminderMsg">用户名或密码不能为空！</string>
    <string name="reminderMsg2">所有字段都必须填写！</string>
    <string name="save">保存</string>
    <string name="select_language">选择语言</string>
    <string name="setting">设置</string>
    <string name="success">成功</string>
    <string name="txtCreatedAt">"创建时间: "</string>
    <string name="txtCreator">"创建者: "</string>
    <string name="txtErrorMsg">"错误信息: "</string>
    <string name="txtMachDowntime">"机器停机时间: "</string>
    <string name="txtStatus">"状态: "</string>
    <string name="txtmachineId">"机器编号: "</string>
    <string name="updConnection">URL 连接已更新为:</string>
    <string name="updateSuccess">您已接受 1 个工单！</string>
    <string name="username">用户名</string>
    <string name="welcMsg">欢迎回来！</string>
    <string name="welcome">欢迎！</string>
</resources>
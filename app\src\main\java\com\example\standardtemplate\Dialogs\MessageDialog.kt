package com.example.standardtemplate.Dialogs

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.Window
import android.widget.Button
import android.widget.TextView
import com.example.standardtemplate.R

object MessageDialog {
    /**
     * showMessageDialog function --> show a message dialog with a title and a message
     * @param context the context of the activity or fragment
     * @param title the title of the dialog
     * @param message the message to be displayed in the dialog
     * @param onOkClicked the callback function to be executed when the "OK" button is clicked
     */
    fun showMessageDialog(context: Context, title: String, message: String, onOkClicked: (() -> Unit)? = null) {
        val dialog = Dialog(context)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_message, null)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(view)

        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvMessage = view.findViewById<TextView>(R.id.tvMessage)
        val btnOk = view.findViewById<Button>(R.id.btnOk)

        tvTitle.text = title
        tvMessage.text = message

        btnOk.setOnClickListener {
            onOkClicked?.invoke() // Call the callback function if provided
            dialog.dismiss()
        }

        dialog.show()
    }
}

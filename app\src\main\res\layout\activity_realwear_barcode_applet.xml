<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Barcode Applet Demo Layout -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@color/white"
    tools:context=".Activities.RealWear.BarcodeAppletActivity">

    <!-- Title Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_barcode_applet_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_barcode_applet_description"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="20dp"
        android:lineSpacingMultiplier="1.2" />

    <!-- Scanning Options Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_barcode_scan_options"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp" />

        <!-- Default Scan Button -->
        <Button
            android:id="@+id/btnScanBarcode"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_barcode_scan_default"
            android:textSize="16sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- QR Only Scan Button -->
        <Button
            android:id="@+id/btnScanQROnly"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_barcode_scan_qr_only"
            android:textSize="16sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- All Formats Scan Button -->
        <Button
            android:id="@+id/btnScanAllFormats"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_barcode_scan_all_formats"
            android:textSize="16sp"
            android:background="@drawable/input_field_background"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp"
            android:gravity="center" />

        <!-- Clear Results Button -->
        <Button
            android:id="@+id/btnClearResults"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:text="@string/realwear_barcode_clear_results"
            android:textSize="16sp"
            android:background="@drawable/dialog_background"
            android:textColor="@color/black"
            android:gravity="center" />

    </LinearLayout>

    <!-- Results Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_barcode_results_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="12dp" />

        <!-- Scrollable Results Display -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:padding="12dp">

            <TextView
                android:id="@+id/barcode_textview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/realwear_barcode_ready_to_scan"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:lineSpacingMultiplier="1.4"
                android:fontFamily="monospace" />

        </ScrollView>

    </LinearLayout>

    <!-- Instructions Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background_light"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/realwear_barcode_instructions_title"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/realwear_barcode_instructions"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:lineSpacingMultiplier="1.3" />

    </LinearLayout>

    <!-- Back Button -->
    <Button
        android:id="@+id/btnBack"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="@string/btnBack"
        android:textSize="16sp"
        android:background="@drawable/dialog_background"
        android:textColor="@color/black"
        android:drawableStart="@drawable/ic_back"
        android:drawablePadding="8dp"
        android:gravity="center" />

</LinearLayout>


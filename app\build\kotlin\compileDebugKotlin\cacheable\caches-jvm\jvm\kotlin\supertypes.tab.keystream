Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivityQcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivityTcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapterecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolderCcom.example.standardtemplate.Activities.Login_Setting.LoginActivityKcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter\com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolderKcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivityHcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivityEcom.example.standardtemplate.Activities.RealWear.ActionButtonActivityEcom.example.standardtemplate.Activities.RealWear.AudioCaptureActivityCcom.example.standardtemplate.Activities.RealWear.BNFGrammarActivityFcom.example.standardtemplate.Activities.RealWear.BarcodeAppletActivityEcom.example.standardtemplate.Activities.RealWear.CameraAppletActivityGcom.example.standardtemplate.Activities.RealWear.DocumentAppletActivityAcom.example.standardtemplate.Activities.RealWear.HelpMenuActivityJcom.example.standardtemplate.Activities.RealWear.KeyboardDictationActivityJcom.example.standardtemplate.Activities.RealWear.MicrophoneReleaseActivityDcom.example.standardtemplate.Activities.RealWear.MovieAppletActivityEcom.example.standardtemplate.Activities.RealWear.RealWearMainActivityIcom.example.standardtemplate.Activities.RealWear.SpeechRecognizerActivityEcom.example.standardtemplate.Activities.RealWear.TextToSpeechActivityAcom.example.standardtemplate.Activities.Register.RegisterActivity?com.example.standardtemplate.Libraries.FirebaseMessagingServiceCcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivityGcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate:com.example.standardtemplate.Libraries.NotificationService7com.example.standardtemplate.Libraries.TicketMonitoring                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
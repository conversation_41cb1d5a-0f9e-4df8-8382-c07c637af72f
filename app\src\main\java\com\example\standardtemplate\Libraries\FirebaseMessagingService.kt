package com.example.standardtemplate.Libraries

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ContentValues.TAG
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.example.standardtemplate.Activities.Login_Setting.LoginActivity
import com.example.standardtemplate.Activities.NewTickets.NewTicketListActivity
import com.example.standardtemplate.Dialogs.MessageDialog.showMessageDialog
import com.example.standardtemplate.Libraries.Managers.UserInfoManager
import com.example.standardtemplate.Models.FcmTokenRequest
import com.example.standardtemplate.R
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class FirebaseMessagingService: FirebaseMessagingService() {
    /**
     * Called when a message is received from FCM while the app is in the foreground.
     * Handles both notification and data messages.
     */
    override fun onMessageReceived(message: RemoteMessage) {
        val data = message.data
        Log.d("FCM", "Received topic message: $data")

        val ticketCount = data["ticket_count"]?.toIntOrNull() ?: 0
        if (ticketCount > 0) {
            val title = data["title"] ?: message.notification?.title ?: "New Ticket"
            val body = data["body"] ?: message.notification?.body ?: "$ticketCount new ticket${if (ticketCount > 1) "s" else ""} created!"

            sendNotification(title, body)
        }
    }

    /**
     * Called when a new FCM registration token is generated.
     * You can use this to send the new token to your server.
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d("FCM", "Refreshed token: $token")
    }

    /**
     * Prepares an intent to launch the correct activity based on login state.
     * If user is logged in, go to NewTicketListActivity, otherwise go to LoginActivity.
     */
    //check username exist before bring the user to the ui
    private fun ticketTapIntent(): Intent {
        val username = UserInfoManager.username.toString()
        return if (username.isBlank()) {
            // user not logged-in → go to login screen
            Intent(this, LoginActivity::class.java)
        } else {
            // user logged-in → go straight to home / tickets
            Intent(this, NewTicketListActivity::class.java).apply {
                putExtra("navigateTo", "tickets")
            }
        }.apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
    }

    /**
     * Shows a notification with provided title and message content.
     * Creates a notification channel if required (Android 8+).
     */
    private fun sendNotification(title: String?, messageBody: String?) {
        val channelId = "default_channel"
        // PendingIntent that will be triggered when the user taps the notification
        val pendingTap = PendingIntent.getActivity(this, 0, ticketTapIntent() , PendingIntent.FLAG_IMMUTABLE)
        // Build the notification
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.application_icon)
            .setContentTitle(title ?: "Standard Template")
            .setContentText(messageBody)
            .setContentIntent(pendingTap)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Android O+ requires a notification channel
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(channelId, "Default Channel", NotificationManager.IMPORTANCE_HIGH)
            notificationManager.createNotificationChannel(channel)
        }

        // show notification
        notificationManager.notify(0, notificationBuilder.build())
    }

}
iapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketDetailsActivity.ktfapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketListActivity.ktiapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketAdapter.kteapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketDto.kt_app/src/main/java/com/example/standardtemplate/Activities/Login_Setting/Dialog/SettingDialog.ktXapp/src/main/java/com/example/standardtemplate/Activities/Login_Setting/LoginActivity.kt`app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Adapter/NewTicketAdapter.kt[app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Dialog/CreateDialog.ktfapp/src/main/java/com/example/standardtemplate/Activities/NewTickets/Interface/CreateDialogListener.kt`app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketDetailsActivity.kt]app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketListActivity.ktZapp/src/main/java/com/example/standardtemplate/Activities/RealWear/ActionButtonActivity.ktZapp/src/main/java/com/example/standardtemplate/Activities/RealWear/AudioCaptureActivity.ktXapp/src/main/java/com/example/standardtemplate/Activities/RealWear/BNFGrammarActivity.kt[app/src/main/java/com/example/standardtemplate/Activities/RealWear/BarcodeAppletActivity.ktZapp/src/main/java/com/example/standardtemplate/Activities/RealWear/CameraAppletActivity.kt\app/src/main/java/com/example/standardtemplate/Activities/RealWear/DocumentAppletActivity.ktVapp/src/main/java/com/example/standardtemplate/Activities/RealWear/HelpMenuActivity.kt_app/src/main/java/com/example/standardtemplate/Activities/RealWear/KeyboardDictationActivity.kt_app/src/main/java/com/example/standardtemplate/Activities/RealWear/MicrophoneReleaseActivity.ktYapp/src/main/java/com/example/standardtemplate/Activities/RealWear/MovieAppletActivity.ktZapp/src/main/java/com/example/standardtemplate/Activities/RealWear/RealWearMainActivity.kt^app/src/main/java/com/example/standardtemplate/Activities/RealWear/SpeechRecognizerActivity.ktZapp/src/main/java/com/example/standardtemplate/Activities/RealWear/TextToSpeechActivity.ktVapp/src/main/java/com/example/standardtemplate/Activities/Register/RegisterActivity.ktLapp/src/main/java/com/example/standardtemplate/Dialogs/ConfirmationDialog.ktGapp/src/main/java/com/example/standardtemplate/Dialogs/MessageDialog.ktEapp/src/main/java/com/example/standardtemplate/Libraries/ApiClient.ktHapp/src/main/java/com/example/standardtemplate/Libraries/ApiInterface.ktTapp/src/main/java/com/example/standardtemplate/Libraries/FirebaseMessagingService.ktXapp/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/BaseActivity.kt\app/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/StandardTemplate.ktTapp/src/main/java/com/example/standardtemplate/Libraries/Managers/UserInfoManager.ktNapp/src/main/java/com/example/standardtemplate/Libraries/NotificationHelper.ktOapp/src/main/java/com/example/standardtemplate/Libraries/Notificationservice.ktRapp/src/main/java/com/example/standardtemplate/Libraries/RealWear/DocumentUtils.ktWapp/src/main/java/com/example/standardtemplate/Libraries/StandardFunction/SharedPref.ktLapp/src/main/java/com/example/standardtemplate/Libraries/TicketMonitoring.ktJapp/src/main/java/com/example/standardtemplate/Models/AcceptedTicketDto.ktHapp/src/main/java/com/example/standardtemplate/Models/FcmTokenRequest.ktBapp/src/main/java/com/example/standardtemplate/Models/LoginInfo.ktFapp/src/main/java/com/example/standardtemplate/Models/LoginResponse.ktFapp/src/main/java/com/example/standardtemplate/Models/NewTicketInfo.ktNapp/src/main/java/com/example/standardtemplate/Models/NewTicketListResponse.ktEapp/src/main/java/com/example/standardtemplate/Models/RegisterInfo.ktIapp/src/main/java/com/example/standardtemplate/Models/RegisterResponse.ktCapp/src/main/java/com/example/standardtemplate/Models/TicketDone.ktDapp/src/main/java/com/example/standardtemplate/Models/UserDetails.ktWapp/src/main/java/com/example/standardtemplate/Activities/Login_Setting/ReturnResult.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
package com.example.standardtemplate.Activities.RealWear

import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.WindowManager
import android.widget.ImageView
import android.widget.Toast
import com.example.standardtemplate.Libraries.LanguageSetting.BaseActivity
import com.example.standardtemplate.R

/**
 * RealWear Action Button功能Activity
 * 
 * 此Activity演示如何与RealWear HMT设备上的操作按钮进行交互
 * 当用户按下或释放操作按钮时，界面会显示相应的视觉反馈
 * 
 * RealWear Development Software, Source Code and Object Code
 * (c) RealWear, Inc. All rights reserved.
 * Contact <EMAIL> for further information about the use of this code.
 * 
 * 功能说明：
 * - 监听RealWear设备的操作按钮事件
 * - 按钮按下时显示红色图标
 * - 按钮释放时显示默认图标
 * - 禁用操作按钮的默认行为
 */
class ActionButtonActivity : BaseActivity() {
    
    companion object {
        /**
         * RealWear设备操作按钮的键码
         * 这是RealWear设备特定的键码值
         */
        private const val ACTION_BTN_KEY_CODE = 500
        private const val TAG = "ActionButtonActivity"
    }
    
    // UI组件
    private lateinit var mActionButtonImageView: ImageView
    
    /**
     * Activity创建时调用
     * 
     * @param savedInstanceState 保存的实例状态，参见Android文档
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "ActionButtonActivity onCreate started")

        // 设置全屏模式，适配RealWear设备显示
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )

        // 设置布局文件
        setContentView(R.layout.activity_realwear_action_button)

        // 初始化UI组件
        initializeViews()

        Log.d(TAG, "ActionButtonActivity onCreate completed")
    }
    
    /**
     * 初始化界面组件
     */
    private fun initializeViews() {
        mActionButtonImageView = findViewById(R.id.actionButtonImageView)
    }
    

    
    /**
     * Activity恢复时调用 - 参见Android文档
     * 重置按钮图标为默认状态
     */
    override fun onResume() {
        super.onResume()
        // 设置默认的未选中图标
        mActionButtonImageView.setImageResource(R.drawable.radio_button_unchecked)
    }
    
    /**
     * Activity暂停时调用 - 参见Android文档
     * 结束Activity以释放资源
     */
    override fun onPause() {
        super.onPause()
        finish()
    }
    
    /**
     * 按键按下事件处理 - 参见Android文档
     *
     * @param keyCode 按键码，来自event.getKeyCode()
     * @param event 按键事件描述
     * @return 如果是操作按钮按下则返回true，否则返回false
     */
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        Log.d(TAG, "onKeyDown: keyCode = $keyCode, expected = $ACTION_BTN_KEY_CODE")
        // 记录所有按键事件，不管是什么键
        Log.d(TAG, "Key event details: keyCode=$keyCode, action=${event?.action}, repeatCount=${event?.repeatCount}")

        when (keyCode) {
            ACTION_BTN_KEY_CODE -> {
                Log.d(TAG, "Action button pressed!")

                mActionButtonImageView.setImageResource(R.drawable.radio_button_red)
                return true
            }
            // 添加其他可能的 RealWear 按键码
            KeyEvent.KEYCODE_BUTTON_1,
            KeyEvent.KEYCODE_BUTTON_2,
            KeyEvent.KEYCODE_BUTTON_3,
            KeyEvent.KEYCODE_BUTTON_4,
            KeyEvent.KEYCODE_BUTTON_5,
            KeyEvent.KEYCODE_BUTTON_6,
            KeyEvent.KEYCODE_BUTTON_7,
            KeyEvent.KEYCODE_BUTTON_8,
            KeyEvent.KEYCODE_DPAD_CENTER,
            KeyEvent.KEYCODE_ENTER,
            KeyEvent.KEYCODE_SPACE -> {
                Log.d(TAG, "Generic button pressed: $keyCode")

                mActionButtonImageView.setImageResource(R.drawable.radio_button_red)
                return true
            }
            else -> {
                // 显示任何其他按键，帮助调试
                Log.d(TAG, "Unknown key pressed: $keyCode")

            }
        }

        return super.onKeyDown(keyCode, event)
    }
    
    /**
     * 按键释放事件处理 - 参见Android文档
     *
     * @param keyCode 按键码，来自event.getKeyCode()
     * @param event 按键事件描述
     * @return 如果是操作按钮释放则返回true，否则返回false
     */
    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        Log.d(TAG, "onKeyUp: keyCode = $keyCode, expected = $ACTION_BTN_KEY_CODE")


        when (keyCode) {
            ACTION_BTN_KEY_CODE -> {
                Log.d(TAG, "Action button released!")

                // 操作按钮被释放，恢复默认未选中状态
                mActionButtonImageView.setImageResource(R.drawable.radio_button_unchecked)
                return true
            }
            // 添加其他可能的 RealWear 按键码
            KeyEvent.KEYCODE_BUTTON_1,
            KeyEvent.KEYCODE_BUTTON_2,
            KeyEvent.KEYCODE_BUTTON_3,
            KeyEvent.KEYCODE_BUTTON_4,
            KeyEvent.KEYCODE_BUTTON_5,
            KeyEvent.KEYCODE_BUTTON_6,
            KeyEvent.KEYCODE_BUTTON_7,
            KeyEvent.KEYCODE_BUTTON_8 -> {
                Log.d(TAG, "Generic button released: $keyCode")
                Toast.makeText(this, "通用按钮释放: $keyCode", Toast.LENGTH_SHORT).show()
                mActionButtonImageView.setImageResource(R.drawable.radio_button_unchecked)
                return true
            }
        }

        return super.onKeyUp(keyCode, event)
    }
}


<?xml version="1.0" encoding="utf-8"?>
<!-- RealWear Action Button演示界面布局 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white"
    android:contentDescription="hf_no_ptt_home"
    tools:context=".Activities.RealWear.ActionButtonActivity">

    <!-- 标题文本 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/realwear_action_button_title"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 说明文本 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/realwear_action_button_description"
        android:textSize="16sp"
        android:textColor="@color/black"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2" />

    <!-- 操作按钮状态显示 -->
    <ImageView
        android:id="@+id/actionButtonImageView"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="24dp"
        android:contentDescription="@string/realwear_action_button_status"
        android:src="@drawable/radio_button_unchecked"
        android:scaleType="fitCenter" />

    <!-- 状态说明文本 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/realwear_action_button_status_text"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:gravity="center"
        android:layout_marginBottom="32dp" />

    <!-- 使用说明 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/realwear_action_button_instructions"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:gravity="center"
        android:padding="16dp"
        android:background="@drawable/rounded_background_light"
        android:lineSpacingMultiplier="1.3" />

</LinearLayout>

